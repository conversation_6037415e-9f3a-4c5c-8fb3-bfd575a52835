require 'twilio-ruby'
require 'nanoid'
require 'sendgrid-ruby'

class User < ApplicationRecord
  # acts_as_google_authenticated issuer: 'Praja', method: :phone_with_label, google_secret_column: :mfa_secret
  include AASM
  has_paper_trail(
    meta: {
      tag: :change_tag
    },
    ignore: [:total_following_count, :total_followers_count]
  )
  include Hashid::Rails
  include UserSearchConcern
  include AutoRecommendFrames
  include SendGrid
  include UserPostersTrial
  include UserFanRequests
  include FetchCircleLayoutsConcern
  include UsersConcern
  include Elections2024
  include ElectionConstituencyStatus
  include Manifesto
  include Prepoll
  include UpgradePackageConcern
  include CohortBasedCampaignConcern
  include UserLayoutConcern

  before_save :update_internal_journalist_enabled_at, :trim_white_spaces, :set_is_internal
  after_update_commit :notify_users
  after_commit :index_for_search, :flush_cache, :send_to_mixpanel, :check_truecaller_image
  after_update_commit :check_signed_up_user, :clear_token_caches_if_inactivated, :update_invite_card,
                      :frame_recommendations, :send_to_floww, :update_floww_lead_score
  before_create :set_default_values

  transliteration name_attr: :name

  belongs_to :photo, -> { where active: true }, optional: true
  belongs_to :poster_photo, polymorphic: true, optional: true
  belongs_to :poster_photo_with_background, polymorphic: true, optional: true
  belongs_to :family_frame_photo, polymorphic: true, optional: true
  belongs_to :hero_frame_photo, polymorphic: true, optional: true
  # affiliated_party_circle_id is the circle_id of the party circle user is affiliated to
  belongs_to :affiliated_party_circle, class_name: 'Circle', foreign_key: 'affiliated_party_circle_id', optional: true
  validate :check_name_after_trim_white_space
  validates :email, format: { with: /\A[a-zA-Z\d._-]+@[a-zA-Z]+(\.[a-z\d\-]+)*\.[a-z]{2,3}+/ }, allow_blank: true
  validate :user_poster_photos
  enum gender: %i[male female other], _suffix: true
  enum status: {
    pre_signup: 'pre_signup',
    auto_signed_up: 'auto_signed_up',
    active: 'active',
    banned: 'banned',
    deleted: 'deleted',
  }, _suffix: true

  aasm column: :status, enum: true do
    state :pre_signup, initial: true
    state :auto_signed_up
    state :active
    state :banned
    state :deleted

    event :activate do
      transitions from: [:pre_signup, :auto_signed_up, :deleted], to: :active
    end
    event :ban do
      transitions from: :active, to: :banned
    end
    event :unban do
      transitions from: :banned, to: :active
    end
    event :delete do
      transitions from: [:pre_signup, :auto_signed_up, :active], to: :deleted
    end
  end

  # has_one :refer_user, class_name: 'User', foreign_key: 'refer_user_id'
  has_many :user_circles
  has_many :circles, through: :user_circles
  has_many :user_groups, -> { where active: true }
  has_many :posts, -> { where active: true }
  has_one :user_badge, -> { where active: true }
  has_many :user_roles, -> { where(active: true, verification_status: :verified) }
  has_many :roles, through: :user_roles
  has_many :user_roles_including_unverified, -> { where active: true }, class_name: 'UserRole'
  has_many :followers, class_name: 'UserFollower', foreign_key: 'user_id'
  has_many :following, class_name: 'UserFollower', foreign_key: 'follower_id'
  has_many :user_tokens
  has_many :user_followers
  has_many :circles_relations
  has_many :singular_data
  has_many :user_points_ledgers
  has_one :village, primary_key: :village_id, foreign_key: :id, class_name: 'Circle', autosave: false
  has_one :mandal, primary_key: :mandal_id, foreign_key: :id, class_name: 'Circle', autosave: false
  # has_one :district, primary_key: :district_id, foreign_key: :id, class_name: 'Circle', autosave: false
  # has_one :state, primary_key: :state_id, foreign_key: :id, class_name: 'Circle', autosave: false
  has_one :user_plan
  has_one :birth_place, primary_key: :birth_place_id, foreign_key: :id, class_name: 'Circle', autosave: false
  has_many :user_circle_permission_groups
  has_many :leader_projects
  has_many :metadatum, as: :entity
  has_many :orders
  has_many :user_product_subscriptions
  has_many :videos, as: :user

  has_many :user_poster_layouts, as: :entity
  has_one :active_user_poster_layout, -> { where(active: true) }, as: :entity, class_name: 'UserPosterLayout'
  has_many :user_device_tokens
  has_one :last_user_device_token, -> { order(id: :desc) }, class_name: 'UserDeviceToken'
  has_one :latest_device, through: :last_user_device_token, source: :mapped_device

  attribute :follows
  attribute :blocked
  attribute :loggedInUser
  attribute :photo
  # attribute :village
  # attribute :location_circles
  attribute :gender_new
  attribute :badge
  attribute :avatar_color
  attribute :followers_count
  attribute :following_count
  attribute :user_referral
  attribute :has_profile_picture
  attribute :source_of_follow

  scope :active, -> { where(status: :active) }

  CACHE_KEY = 'user_v28'

  COUNT_TO_BE_INVITED = 20

  BADGE_CARD_ENABLED_DATE = '16-02-2024'

  USER_IDS_OF_UPDATED_FOLLOWERS = 'user_ids_of_updated_followers'

  MAX_SEEN_OF_SUGGESTED_USERS_LIST = 2

  MAX_LEN_OF_OLD_SUGGESTED_USERS = 30

  MIN_LEN_OF_SUGGESTED_USERS = 3

  MAX_LEN_OF_SUGGESTED_USERS = 5

  SUGGESTED_LIST_1 = 'SL-1'

  SUGGESTED_LIST_2 = 'SL-2'

  SUGGESTED_LIST_3 = 'SL-3'

  SUGGESTED_LIST_4 = 'SL-4'

  SUGGESTED_LIST_5 = 'SL-5'

  SUGGESTED_LIST_6 = 'SL-6'

  SUGGESTED_LIST_7 = 'SL-7'

  SUGGESTED_LIST_8 = 'SL-8'

  SUGGESTED_LIST_9 = 'SL-9'

  # older app build numbers hash to get app build numbers lower than 1.16.2 and greater than app version 1.12.6
  # getting app_build_number in headers from 1.16.2
  OLDER_APP_BUILD_NUMBERS = {
    "1.17.0": 168,
    "1.16.3": 167,
    "1.16.2": 165,
    "1.16.1": 161,
    "1.16.0": 158,
    "1.15.4": 157,
    "1.15.3": 154,
    "1.15.2": 149,
    "1.15.1": 148,
    "1.15.0": 147,
    "1.14.5": 143,
    "1.14.4": 142,
    "1.14.3": 141,
    "1.14.2": 140,
    "1.14.1": 139,
    "1.14.0": 136,
    "1.13.1": 135,
    "1.13.0": 134,
    "1.12.6": 133
  }

  # Note:: if we add any key in VALID_ORIGINAL_POSTER_PHOTO_KEYS then we need to add that key in the
  # BG_REMOVAL_POSTER_PHOTO_KEYS to remove the background of that photo
  # To know which keys are associated with each other refer to POSTER_PHOTO_KEY_MAPPING
  VALID_ORIGINAL_POSTER_PHOTO_KEYS = [
    Constants.poster_photo_without_background_original_key,
    Constants.hero_frame_photo_original_key,
    Constants.family_frame_photo_original_key
  ]

  BG_REMOVAL_POSTER_PHOTO_KEYS = [
    Constants.poster_photo_without_background_key,
    Constants.hero_frame_photo_key,
    Constants.family_frame_photo_key
  ]

  POSTER_PHOTO_KEY_MAPPING = {
    Constants.poster_photo_without_background_original_key => Constants.poster_photo_without_background_key,
    Constants.hero_frame_photo_original_key => Constants.hero_frame_photo_key,
    Constants.family_frame_photo_original_key => Constants.family_frame_photo_key
  }

  def can_login?
    active_status? || may_activate?
  end

  # Temp function till data migration
  def get_unread_notifications_count
    if self.unread_notifications_count.nil?
      self.unread_notifications_count = Notification.where(user: self, active: true, read: false).count
      self.update_column(:unread_notifications_count, self.unread_notifications_count)
    end

    self.unread_notifications_count
  end

  def is_eligible_for_trend_feedback?
    !$redis.exists(Constants.get_trend_feedback_eligibility_redis_key(self.id))
  end

  def self.ransackable_associations(auth_object = nil)
    ['birth_place', 'circles', 'circles_relations', 'followers', 'following', 'leader_projects', 'metadatum', 'orders', 'photo', 'poster_photo', 'poster_photo_with_background', 'posts', 'singular_data', 'user_badge', 'user_circle_permission_groups', 'user_circles', 'user_followers', 'user_groups', 'user_points_ledgers', 'user_product_subscriptions', 'user_roles', 'user_tokens', 'versions', 'village']
  end

  def user_poster_photos
    if (poster_photo_id_changed? || poster_photo_id_was.nil?) && poster_photo.present? && !poster_photo.valid?
      errors.add(:poster_photo, ' is given and is invalid')
    end

    if (poster_photo_with_background_id_changed? || poster_photo_with_background_id_was.nil?) && poster_photo_with_background.present? && !poster_photo_with_background.valid?
      errors.add(:poster_photo_with_background, ' is given and is invalid')
    end
  end

  def send_to_floww
    # TODO: Uncomment below code after implementing Floww
    Floww::UpdateContact.perform_async(id) if get_floww_contact_id.present?
  end

  def update_floww_lead_score
    # if affiliated_party_circle_id updated from nil to some value or some value to nil then only call this
    UpdateLeadScore.perform_async(id) if affiliated_circle_id_changed_between_nil_and_integer? || saved_change_to_status?
  end

  # Custom condition to check specific changes in affiliated_party_circle_id
  #  if affiliated_party_circle_id updated from nil to some value or some value to nil then only return true
  def affiliated_circle_id_changed_between_nil_and_integer?
    affiliated_party_circle_id_previously_changed? &&
      (
        (affiliated_party_circle_id.nil? && affiliated_party_circle_id_previous_change.first.is_a?(Integer)) ||
          (affiliated_party_circle_id.is_a?(Integer) && affiliated_party_circle_id_previous_change.first.nil?)
      )
  end

  def otp_key
    "user_otp_#{self.id}"
  end

  def email_otp_key
    "user_email_otp_#{self.id}"
  end

  def set_is_internal
    self.internal = self.class.is_internal(self.phone)
  end

  def set_default_values
    # self.mfa_secret = ROTP::Base32.random_base32
  end

  def get_birth_day
    birthday = self.dob.strftime('%d %B %Y')
    age = (Date.current - Date.parse("#{self.dob}")).to_i / 365

    birthday_str = "#{birthday}" + "\n" + "(age #{age} years)"
    return birthday_str
  end

  def clear_token_caches_if_inactivated
    return if active_status?

    access_tokens = user_tokens.map(&:access_token)
    $redis.hdel('user_tokens', access_tokens) if access_tokens.present?

    user_tokens.update_all(active: false)
  end

  def flush_cache
    Rails.cache.delete([CACHE_KEY, id])
  end

  def get_post_view_ids
    # Get last 2 days post views of the user

    post_view_ids = []
    no_of_days_to_fetch_post_views = 3
    i = 1
    while i <= no_of_days_to_fetch_post_views
      date_key = (Time.zone.now - (i - 1).days).strftime('%Y-%m-%d')
      user_date_post_views_queue = Constants.user_date_post_views_queue_redis_key(id, date_key)
      viewed_post_ids = $redis.smembers(user_date_post_views_queue)
      post_view_ids += viewed_post_ids.map(&:to_i) if viewed_post_ids.present?

      i += 1
    end

    post_view_ids
  end

  def self.get_feed_item(user_id)
    Rails.cache.fetch([CACHE_KEY, user_id], expires_in: 1.month) do
      fetch_user_data(user_id)
    end
  end

  def self.get_users_batch(user_ids)
    user_cache_keys = user_ids.map { |id| [CACHE_KEY, id] }
    users_from_cache = Rails.cache.fetch_multi(*user_cache_keys, expires_in: 1.month) do |user_cache_key|
      user_id = user_cache_key.last
      fetch_user_data(user_id)
    end
    users_from_cache.values
  end

  def self.fetch_user_data(user_id)
    u = self.find(user_id)
    u.phone = u.generate_random_phone
    u.badge = u.get_badge_role&.get_json
    u.photo&.compressed_url!(size: 200)
    u
  end

  def generate_random_phone
    # rand(7000000000..9999999999)
    if Current.logged_in_user.present? && Current.logged_in_user&.get_all_internal_permissions.include?('super_admin')
      self.phone
    else
      1000000000
    end
  end

  def get_user_joined_party_circle_ids
    self.circles.where(level: :political_party).pluck(:id)
  end

  def get_user_joined_interest_circle_ids
    self.circles.where(circle_type: :interest).pluck(:id)
  end

  def get_user_joined_circle_ids_for_posters_feed_for_you(add_limit: false, add_public_and_state_circle_id: false)
    query = self.circles
    query = query.limit(100) if add_limit
    ids = query.pluck(:id)
    ids << Constants.public_circle_id if add_public_and_state_circle_id
    ids << self.state_id if add_public_and_state_circle_id && self.state_id.present?
    ids
  end

  def get_user_joined_state_level_leader_ids
    self.circles.joins("INNER JOIN circles_relations on circles.id = circles_relations.first_circle_id and
                        circles_relations.relation = 'Leader2State'").pluck(:first_circle_id)
  end

  def get_user_joined_leader_ids(add_limit: false)
    query = self.user_circles.joins(:circle).where(circles: { level: :political_leader, active: true })
    query = query.order(id: :desc).limit(100) if add_limit
    query.pluck(:circle_id)
  end

  def get_user_joined_party_and_leader_ids(add_limit: false)
    query = self.user_circles.joins(:circle).where(circles: { level: [:political_leader, :political_party],
                                                              active: true })
    query = query.order(level: :asc, id: :desc).limit(100) if add_limit
    query.pluck(:circle_id)
  end

  def get_user_blocked_ids
    BlockedUser.where(user_id: self.id, active: 1).pluck(:blocked_user_id)
  end

  def get_user_joined_circles_owner_ids
    UserCirclePermissionGroup.joins("INNER JOIN user_circles on user_circles.circle_id =
        user_circle_permission_groups.circle_id and user_circles.user_id = #{self.id}")
                             .where(permission_group_id: Constants.owner_permission_group_id).pluck(:user_id)
  end

  def is_circle_owner?
    UserCirclePermissionGroup.where(user_id: self.id, permission_group_id: Constants.owner_permission_group_id).exists?
  end

  def get_user_circle_permission_as_owner
    UserCirclePermissionGroup.where(user_id: self.id, permission_group_id: Constants.owner_permission_group_id).first
  end

  def get_following_weight
    is_badge_user = self.get_badge_role.present?
    count = self.following_count
    weight = 10
    case true
    when count < 150
      is_badge_user ? 0.8 * weight : 0.8 * weight
    when count >= 150 && count < 300
      is_badge_user ? 0.5 * weight : 0.5 * weight
    when count >= 300 && count < 450
      is_badge_user ? 0.5 * weight : 0.5 * weight
    when count >= 450 && count < 600
      is_badge_user ? 0.1 * weight : 0.3 * weight
    else
      0.1 * weight
    end
  end

  def get_local_leaders_weight
    joined_local_leaders_count = self.get_user_joined_leader_ids.count
    case true
    when joined_local_leaders_count == 1
      33
    when joined_local_leaders_count == 2
      11
    when joined_local_leaders_count > 2
      4
    else
      0
    end
  end

  def get_state_leaders_weight
    joined_state_leaders_count = self.get_user_joined_state_level_leader_ids.count
    case true
    when joined_state_leaders_count == 1
      15
    when joined_state_leaders_count == 2
      5
    when joined_state_leaders_count > 2
      2
    else
      0
    end
  end

  def get_party_weight
    joined_political_parties_count = self.get_user_joined_party_circle_ids.count
    case true
    when joined_political_parties_count == 1
      1.1
    when joined_political_parties_count == 2
      1.5
    when joined_political_parties_count > 2
      1.1
    else
      0
    end
  end

  def update_internal_journalist_enabled_at
    self.internal_journalist_enabled_at = Time.zone.now if internal_journalist_changed? && internal_journalist?
  end

  def trim_white_spaces
    self.name = self.name&.strip
  end

  def common_properties_for_mixpanel_and_app_open
    badge_user_role = self.get_badge_role_including_unverified
    if badge_user_role
      badge = 'Yes'
      role = badge_user_role.get_role_name || 'null'
      role_id = badge_user_role.role_id || 0
      grade = badge_user_role.get_readable_grade_level
      badge_verification_status = badge_user_role.verification_status
    else
      badge = 'No'
      role = 'null'
      role_id = 0
      grade = 'null'
      badge_verification_status = 'null'
    end

    subscription_status = get_subscription_status

    rm_user = get_rm_user

    {
      "badge": badge,
      "badge_verification_status": badge_verification_status,
      "badge_role": role,
      "badge_role_id_v2": role_id,
      "grade": grade,
      "has_profile_picture": self.photo_id.present?,
      "default_feed_backend": self.my_feed_enabled? ? 'my_feed' : 'trending_feed',
      "affiliated_party_circle_id_backend": self.affiliated_party_circle_id,
      "followers_count_backend": self.followers_count,
      "following_count_backend": self.following_count,
      "current_badge_role_ids": self.user_roles.pluck(:role_id),
      "joined_political_party_ids": self.get_user_joined_party_circle_ids,
      "joined_interest_circle_ids": self.get_user_joined_interest_circle_ids,
      "app_build_number_backend": self.app_build_number,
      "posters_subscription_status": subscription_status,
      "rm_user_id": rm_user&.floww_user_id,
      "package_to_pitch": self.should_pitch_yearly_package? ? 'yearly' : nil
    }
  end

  def get_user_profession_ids_and_sub_profession_ids
    user_professions = UserProfession.where(user_id: self.id)

    profession_ids = user_professions.map(&:profession_id).compact.uniq
    sub_profession_ids = user_professions.map(&:sub_profession_id).compact.uniq
    [profession_ids, sub_profession_ids]
  end

  def get_mixpanel_hash
    rm_user = get_rm_user
    premium_pitch = self.premium_pitch
    profession_ids, sub_profession_ids = self.get_user_profession_ids_and_sub_profession_ids
    mixpanel_hash = { "$name": self.name,
                      "$phone": "+91#{self.phone}",
                      "signed_up_backend": !self.pre_signup_status?,
                      "$created": (self.created_at - 5.hours - 30.minutes).strftime('%Y-%m-%dT%TZ'),
                      "is_internal_backend": (self.internal == 1 ? true : false),
                      "village_id_backend": self.village_id,
                      "mandal_id_backend": self.mandal_id,
                      "mla_constituency_id_backend": self.mla_constituency_id,
                      "mp_constituency_id_backend": self.mp_constituency_id,
                      "district_id_backend": self.district_id,
                      "state_id_backend": self.state_id,
                      "village_name_backend": self.village&.name,
                      "mandal_name_backend": self.mandal&.name,
                      "mla_constituency_name_backend": self.mla_constituency&.name,
                      "mp_constituency_name_backend": self.mp_constituency&.name,
                      "district_name_backend": self.district&.name,
                      "state_name_backend": self.state&.name,
                      "village_name_en_backend": self.village&.name_en,
                      "mandal_name_en_backend": self.mandal&.name_en,
                      "mla_constituency_name_en_backend": self.mla_constituency&.name_en,
                      "mp_constituency_name_en_backend": self.mp_constituency&.name_en,
                      "district_name_en_backend": self.district&.name_en,
                      "state_name_en_backend": self.state&.name_en,
                      "user_id_backend": self.id,
                      "floww_contact_id": self.get_floww_contact_id,
                      "lead_type": self.premium_pitch&.lead_type,
                      "rm_user_name": rm_user&.name,
                      "latest_device_name": self.latest_device&.name,
                      "latest_device_price": self.latest_device&.price,
                      "latest_device_launch_date": self.latest_device&.launch_date,
                      "premium_lead_source": premium_pitch&.source,
                      "premium_lead_stage": premium_pitch&.crm_stage,
                      "enabled_charge_ivr_experiment": is_ivr_experiment_user?,
                      "profession_ids": profession_ids,
                      "sub_profession_ids": sub_profession_ids
    }.compact.merge(common_properties_for_mixpanel_and_app_open)

    mixpanel_hash['app_version_backend'] = self.app_version if self.app_version.present?

    mixpanel_hash
  end

  # these params are used in app open analytics which we send in initial data
  def get_app_open_analytics_params
    {
      'is_self_trial_exp_user': !self.has_premium_layout? && self.id % 2 == 0,
    }.merge(common_properties_for_mixpanel_and_app_open)
  end

  def get_subscription_status
    get_subscription_status = SubscriptionUtils.get_subscription_status_for_mixpanel(id)
    case get_subscription_status

    when :in_trial_extension
      'in_trial_extension'
    when :in_premium_extension
      'in_premium_extension'
    when :in_grace_period
      'in_grace_period'
    when :subscribed # if user plan is in current period and last charge is not auth
      'paid'
    when :premium_expired # has ever subscribed and having not auth charge
      'premium_expired'
    when :in_trial # trial end date < current date
      'in_trial'
    when :trial_expired # has ever subscribed and having auth charge only as last one
      'trial_expired'
    when :autopay_setup_no_layout  # user poster layout present but no user plan
      'autopay_setup_no_layout'
    when :auto_pay_not_set_up # auto_pay_not_set_up # user poster layout present but no user plan
      'auto_pay_not_set_up'
    when :no_order_created # if no plan
      'free'
    else
      Honeybadger.notify("Unknown subscription status for user", context: {
        user_id: id,
        subscription_status: get_subscription_status
      } )
      return
    end
  end

  def send_to_mixpanel
    SyncMixpanelUser.perform_async(self.id)
  end

  def to_honeybadger_context
    { user_id: id }
  end

  def check_truecaller_image
    if photo_id.present? && photo.url.include?('https://storage.googleapis.com')
      uri = URI.parse(photo.url)
      response = Net::HTTP.get_response(uri)
      if response.code.to_i != 200
        self.update_column(:photo_id, nil)
      end
    end
  end

  def has_profile_picture
    photo.present?
  end

  def is_test_user?
    @is_test_user ||= $redis.sismember(Constants.get_test_users_redis_key, id)
  end

  def is_ai_feed_enabled?
    is_test_user?
  end

  def is_test_user_for_floww?
    internal? || is_test_user?
  end

  def avatar_color
    self.class.get_avatar_color(id)
  end

  def self.get_avatar_color(user_id)
    colors = ['#3F51B5', '#F9F871', '#FFC05C', '#FF886F', '#F65D91', '#B34FAE', '#6DBAA1', '#659F9D', '#528070', '#4FBE9D', '#0092E4', '#00CAE4', '#68FBD0', '#EB5D63', '#9CA0FF', '#646FD8', '#888EFB', '#E8D5B5', '#8F8CB8', '#001265']
    index = user_id % colors.count
    colors[index]
  end

  def notify_users
    NotifyContactUsers.perform_async(self.id) if saved_change_to_attribute?(:status) && active_status?
  end

  def should_show_circle_suggestions?
    return [false, 0] if $redis.sismember('disable_circle_suggestions', id.to_s)

    disable = false
    marker_date = Time.zone.parse('08-06-2022')
    marker_date = marker_date > self.created_at ? marker_date : self.created_at
    session_count = UserTokenUsage.where('user_token_usages.created_at > ?', marker_date).where(user_id: self.id).count
    # send session_count as 1 if session_count is 0
    session_count = 1 if session_count == 0
    return [true, session_count] if self.is_test_user? && session_count <= 3
    disable = true if session_count > 3
    $redis.sadd('disable_circle_suggestions', id.to_s) if disable

    [!disable, session_count]
  end

  def should_show_contact_suggestions?
    return false if $redis.sismember('disable_contact_suggestions', id.to_s)

    disable = false
    dates = []

    marker_dates = UserTokenUsage.where(user_id: self.id).pluck(:created_at)

    marker_dates.each do |timestamp|
      dates << timestamp.to_date
    end
    dates.uniq!
    disable = true if dates.length > 2
    $redis.sadd('disable_contact_suggestions', id.to_s) if disable
    !disable
  end

  def should_show_badge_card?
    return false if $redis.sismember('disable_badge_card_in_feed', id.to_s)

    disable = false
    marker_date = Time.zone.parse(BADGE_CARD_ENABLED_DATE)
    marker_date = marker_date > self.created_at ? marker_date : self.created_at

    session_count = UserTokenUsage.where('user_token_usages.created_at > ?', marker_date).where(user_id: self.id).count
    return true if self.is_test_user? && session_count <= 2
    disable = true if session_count > 2
    $redis.sadd('disable_badge_card_in_feed', id.to_s) if disable

    !disable
  end

  # commented for now as it is not using anywhere
  # def is_return_user?
  #   return true if $redis.sismember('return_users', id.to_s) # return user has >= 3 sessions
  #
  #   session_count = UserTokenUsage.where(user_id: id).count
  #
  #   if session_count >= 3
  #     $redis.sadd('return_users', id.to_s)
  #     return true
  #   else
  #     return false
  #   end
  # end

  def notify_users_async
    new_contacts = UserInvite.includes(:user).where(phone: phone)
    new_contacts.each do |each_user|
      next unless each_user.user.active_status?
      notification_title = "మీ పరిచయస్తులు #{each_user.name} Praja App లో జాయిన్ అయ్యారు."
      notification_body = ''

      notification = Notification.create!(
        description: notification_title,
        notification_type: :follow,
        user_id: each_user.user_id,
        entity_type: 'user',
        entity_id: id
      )

      payload = {
        "title": "👥 #{notification_title}",
        "body": notification_body,
        "message_channel": 'contact_join',
        "data": {
          "path": "/users/#{id}",
          "circle_notification_id": notification.id.to_s
        }
      }
      GarudaNotification.send_user_notification(each_user.user_id, payload)
    end
  end

  def my_feed_enabled?
    $redis.sismember('my_feed_enabled_district_ids', district_id.to_s)
  end

  def dm_enabled?
    AppVersionSupport.dm_enabled?
  end

  def district_weight_experiment_enabled?
    true

    # enabled = my_feed_enabled? && id % 2 == 0
    #
    # MixpanelIntegration.perform_async(id, { "enabled_district_weight" => enabled }) if enabled
    #
    # enabled
  end

  def is_views_enabled?
    true
  end

  def get_user_analytics_hash(logged_in_user, follower_ids = nil, following_ids = nil, user_blocked_ids = nil, user_blocked_by_ids = nil)

    {
      "user_is_following": self.id == logged_in_user.id ? false : follower_ids.nil? ? UserFollower.where(user_id: logged_in_user.id, follower_id: self.id).exists? : following_ids.include?(self.id),
      "user_is_followed_by": self.id == logged_in_user.id ? false : following_ids.nil? ? UserFollower.where(user_id: self.id, follower_id: logged_in_user.id).exists? : follower_ids.include?(self.id),
      "user_id": self.id,
      "user_name": self.name,
      "user_is_internal": (self.internal == 1 ? true : false),
      "user_village_id": self.village_id,
      "user_mandal_id": self.mandal_id,
      "user_mla_constituency_id": self.mla_constituency_id,
      "user_mp_constituency_id": self.mp_constituency_id,
      "user_district_id": self.district_id,
      "user_state_id": self.state_id,
      "user_village_name_en": self.village&.name_en,
      "user_mandal_name_en": self.mandal&.name_en,
      "user_mla_constituency_name_en": self.mla_constituency&.name_en,
      "user_mp_constituency_name_en": self.mp_constituency&.name_en,
      "user_district_name_en": self.district&.name_en,
      "user_state_name_en": self.state&.name_en,
      "user_badge": self.badge.present? ? 'YES' : 'NO',
      "user_badge_role_id_v2": self.badge.present? ? self.badge['role_id'] : 0,
      "user_grade": self.badge.present? ? self.badge['grade_level'] : 'null',
      "user_affiliated_party_id": self.affiliated_party_circle_id,
      "user_followers_count": self.followers_count,
      "user_following_count": self.following_count,
      "user_app_build_number": self.app_build_number,
      "user_is_blocked": self.id == logged_in_user.id ? false : user_blocked_ids.nil? ? logged_in_user.get_blocked_user_ids.include?(self.id) : user_blocked_ids.include?(self.id),
      "user_is_blocked_by": self.id == logged_in_user.id ? false : user_blocked_by_ids.nil? ? logged_in_user.get_blocked_by_user_ids.include?(self.id) : user_blocked_by_ids.include?(self.id)

    }
  end

  def get_location_info_for_dm(location)
    if location.present? && !(location.name&.include?('ఇతర') || location.name_en&.downcase&.include?('other'))
      {
        id: location.id,
        name: location.name || location.name_en,
        level: location.level
      }
    else
      nil
    end
  end

  def get_user_response_hash_for_dm(logged_in_user, follower_ids = nil, following_ids = nil, user_blocked_ids = nil, user_blocked_by_ids = nil)
    self.photo.compressed_url!(size: 512) if self.photo.present?
    {
      id: self.id,
      name: self.name,
      photo: self.photo,
      avatar_color: self.avatar_color,
      hashid: self.hashid,
      badge: self.badge,
      has_profile_picture: self.has_profile_picture,
      analytics_params: get_user_analytics_hash(logged_in_user, follower_ids, following_ids, user_blocked_ids, user_blocked_by_ids),
      location: get_location_info_for_dm(self.village) || get_location_info_for_dm(self.mandal) ||
        get_location_info_for_dm(self.district)
    }
  end

  def get_permission_group_name_of_user_on_circle(circle)
    permissions_related_to_circle = get_all_circle_permissions(circle.id).map(&:to_sym)

    return :viewer if circle.conversation_type.to_sym == :private_group && !permissions_related_to_circle.include?(:private_group_send_message)

    _, permission_group_id = get_user_permission_group_id_of_circle(circle.id)

    permission_group_name = if permission_group_id.present?
                              permission_group = PermissionGroup.find_by_id(permission_group_id)
                              if permission_group.present?
                                permission_group.name.include?('default') ? :member : permission_group.name.to_sym
                              end
                            end
    permission_group_name || :member
  end

  def get_user_response_hash(app_version, ignore_posts_count: false, include_unverified_badge: false)
    onesignal_auth_hash = OpenSSL::HMAC.hexdigest(
      'sha256',
      Rails.application.credentials[:one_signal_api_token].gsub('Basic ', ''),
      self.id.to_s
    )

    if include_unverified_badge
      user_role = self.get_badge_role_including_unverified
    else
      user_role = self.get_badge_role
    end

    user_badge_json = user_role&.get_json

    {
      id: self.id,
      name: self.name,
      phone: self.phone,
      email: self.email.to_s,
      photo: self.photo,
      signedUp: !self.pre_signup_status?, # Used to push the user to login form or signup form on the App
      verified: self.verified,
      internal: self.internal,
      village: self.village,
      mandal: self.mandal,
      district: self.district,
      avatar_color: self.avatar_color,
      hashid: self.hashid,
      gender: self.gender,
      gender_new: self.gender.to_i,
      badge: user_badge_json,
      show_phone: self.show_phone,
      has_profile_picture: self.has_profile_picture,
      is_feed_experiment_user: self.my_feed_enabled?,
      show_party_suggestions: !AppVersionSupport.supports_profession_selection?,
      onesignal_auth_hash: onesignal_auth_hash,
      exclusive_party_join: false,
      followers_count: self.followers_count,
      following_count: self.following_count,
      posts_count: ignore_posts_count ? 0 : self.get_posts_count,
      skip_party_member_decision: AppVersionSupport.supports_profession_selection?,
      otp: '',
      token: '',
      is_premium: self.eligible_for_premium_features?,
      show_premium_gold_icon: true,
      eligible_for_premium: self.phone != Constants.app_store_account_phone,
    }
  end

  def free_poster_share_count
    PosterShare.joins(:frame)
               .where(user: self)
               .where.not(frame_id: nil)
               .where(frames: { frame_type: [:basic] })
               .count
  end

  def premium_poster_usage_count_after_trial_enabled
    trial_enabled_date = Metadatum.where(entity: self, key: Constants.user_poster_trial_start_date_key).last&.value

    count = 0
    if trial_enabled_date.present?
      count = PosterShare.joins(:frame)
                         .where(user: self)
                         .where.not(frame_id: nil)
                         .where(frames: { frame_type: [:premium, :status] })
                         .where('poster_shares.created_at > ?', trial_enabled_date).count
    end

    count
  end

  def premium_pitch
    return @premium_pitch if @premium_pitch.present?

    @premium_pitch = PremiumPitch.find_by(user_id: self.id)
    return nil if @premium_pitch.nil?

    @premium_pitch.user = self
    @premium_pitch
  end

  # Checks if OE work is enabled for the user
  # OE work is enabled when premium pitch status is 'layout_setup'
  def oe_work_enabled?
    premium_pitch.present? && premium_pitch.layout_setup?
  end

  # Checks if BOE work is enabled for the user
  # BOE work is enabled when premium pitch status is 'badge_setup'or 'badge_setup_no_layout_setup'
  def boe_work_enabled?
    premium_pitch.present? && (premium_pitch.badge_setup? || premium_pitch.badge_setup_no_layout_setup?)
  end

  def rm_workflow_enabled?
    premium_pitch.present? && (premium_pitch.subscribed_no_layout? || premium_pitch.interested? || premium_pitch.rm_draft? || premium_pitch.pending_layout_approval?)
  end

  def get_floww_contact_id
    Metadatum.find_by(entity: self, key: Constants.floww_contact_id_key)&.value
  end

  def get_zoho_contact_id
    Metadatum.find_by(entity: self, key: Constants.zoho_contact_id_key)&.value
  end

  def phone_with_label
    "#{phone}@praja.buzz"
  end

  def gender_new
    gender_before_type_cast
  end

  # def village
  #   Circle.find(village_id) if village_id.present?
  # end

  def mandal
    Circle.find(mandal_id) if mandal_id.present?
  end

  def district
    Circle.find(district_id) if district_id.present?
  end

  def state
    Circle.find(state_id) if state_id.present?
  end

  def mla_constituency
    Circle.find(mla_constituency_id) if mla_constituency_id.present?
  end

  def mp_constituency
    Circle.find(mp_constituency_id) if mp_constituency_id.present?
  end

  def mark_toast_as_close(toast_id)
    UserToastClose.create!(toast_id: toast_id, user: self)
  end

  def populate_location
    return if self.village_id.nil?

    u_v = Circle.find(self.village_id)

    self.mandal_id = u_v&.parent_circle_id
    self.district_id = u_v&.parent_circle&.parent_circle_id
    self.state_id = u_v&.parent_circle&.parent_circle&.parent_circle_id
    m_c = Circle.get_mla_constituency_of_mandal(self.mandal_id)
    self.mla_constituency_id = m_c&.id
    self.mp_constituency_id = m_c&.parent_circle_id
  end

  def update_location
    self.populate_location
    self.save!
  end

  def get_location_circles
    circles.where('circles.circle_type = 0')
  end

  # copy of the above function with the right name
  # not renaming the above function, right now, since it is being used in many places
  def get_badge_affiliated_party_circle_id
    (get_badge_role_including_unverified&.get_badge_user_affiliated_party_circle_id).to_i
  end

  def get_affiliated_location_circle_id
    (get_badge_role&.get_badge_user_affiliated_location_circle_id).to_i
  end

  def get_badge_role
    @badge_role ||= self.user_roles.joins(:role).where(roles: { has_badge: true }, primary_role: true).last
  end

  def get_badge_role_including_unverified
    @badge_role_including_unverified ||= self.user_roles_including_unverified
                                             .joins(:role)
                                             .where(roles: { has_badge: true }, primary_role: true)
                                             .last
  end

  def has_badge_role?
    @has_badge_role ||= get_badge_role.present?
  end

  def get_all_internal_permissions
    InternalPermission.joins(internal_roles: :users).where(users: { id: self.id }).pluck(:identifier)
  end

  def get_all_circle_permissions(circle_id)
    permissions = []
    circle_id, permission_group_id = get_user_permission_group_id_of_circle(circle_id)
    if permission_group_id.present?
      permissions = PermissionGroup.joins(:permission_group_permissions)
                                   .where(id: permission_group_id)
                                   .pluck('permission_group_permissions.permission_identifier')
    end

    permissions_sym = permissions.map(&:to_sym)
    if permissions_sym.include?(:circle_share_poster)
      user_role = get_badge_role
      if user_role.present?
        user_role_affiliated_circle_id = user_role.get_badge_user_affiliated_party_circle_id
        if user_role_affiliated_circle_id != 0 # user has party affiliated badge
          party_circle_id = CirclesRelation.where(first_circle_id: circle_id, active: true, relation: 'Leader2Party').first&.second_circle_id

          target_circle_id = party_circle_id || circle_id

          if user_role_affiliated_circle_id != target_circle_id and not coalited?(user_role_affiliated_circle_id, target_circle_id)
            permissions_sym.delete(:circle_share_poster)
          end
        end
      end
    end

    if permissions_sym.include?(:circle_upload_creative)
      if CirclePackageMapping.where(circle_id: circle_id, active: true)
                             .where('start_date <= :today AND :today <= end_date', today: Time.zone.today).blank?
        permissions_sym.delete(:circle_upload_creative)
      end
    end

    excluded_user_circle_permissions = ExcludedUserCirclePermission.where(user_id: self.id, circle_id: circle_id).pluck(:permission_identifier)
    valid_permissions = permissions_sym - excluded_user_circle_permissions.map(&:to_sym)

    valid_permissions.map(&:to_s)
  end

  def get_poster_subscription_status
    SubscriptionUtils.get_subscription_status(self.id)
  end

  #@deprecated
  def get_subscriptions_status_for_frames(frame_ids)
    SubscriptionUtils.get_subscription_status_for_frames(self.id, frame_ids)
  end

  #@deprecated
  def is_subscribed_to_frame?(frame_ids)
    status = self.get_subscriptions_status_for_frames(frame_ids)
    [:subscribed, :payment_processing].include? status
  end

  #@deprecated
  def has_open_order_on_frame?(frame_ids)
    status = self.get_subscriptions_status_for_frames(frame_ids)

    [:order_created, :payment_failed].include? status
  end

  def is_poster_subscribed
    @is_poster_subscribed ||= get_poster_subscription_status == :subscribed
  end

  def get_user_toast
    toast = Toast.joins("LEFT JOIN user_toast_closes utc ON toasts.id = utc.toast_id AND utc.user_id = #{id}")
                 .where('utc.id IS NULL AND ((? > start_time) OR (end_time IS NOT NULL AND ? < end_time))',
                        Time.zone.now,
                        Time.zone.now)
                 .where('toasts.active = 1 AND circle_id IN (0, ?)', district_id)
                 .select(:id, :title, :body, :start_time, :max_user_sessions, 'IF((toasts.circle_id = 0), 1, 0) as circle_id_order')
                 .order('circle_id_order ASC')
                 .first

    return if toast.blank?

    if toast.max_user_sessions.present?
      # show toast only if no. of user sessions after toast enabled are less than or equal to max_user_sessions
      sessions_count = UserTokenUsage.where('user_token_usages.created_at > ?', toast.start_time).where(user_id: self.id).count

      return if sessions_count.present? && sessions_count > toast.max_user_sessions
    end

    {
      "feed_type": 'feed_toast',
      "feed_item_id": toast.id.to_s,
      "header": toast.title,
      "message": toast.body,
      "icon_size": 18.0,
      "icon_color": 0xff664d03,
      "close_icon_color": 0xff664d03,
      "icon_code_point": 0xf8bf,
      "toast_color": 0xfffff2cd,
      "header_font_color": 0xff664d03,
      "message_font_color": 0xff664d03,
      "header_font_size": 14.0,
      "message_font_size": 12.0,
      "is_removable": true,
      "is_clickable": true,
      "redirect_to": nil,
    }
  end

  def user_referral
    return nil if (!internal_journalist?)

    {
      feed_type: 'referral_card',
      title: 'మీ పరిచయస్తులకు ఇప్పుడే షేర్ చేయండి',
      subtitle: 'అదనపు ఆదాయం పొందండి',
      referral_points: get_referral_points,
      refer_text: 'refer_text',
      share_button_text: 'షేర్ చేయండి',
      see_more_text: 'ఇంకా చూడండి',
    }
  end

  def get_referral_points
    referral_data = get_referral_data
    return referral_data.map { |h| h[:points] }.sum if referral_data.present?
    0
  end

  def get_referral_data
    marker_date = self.internal_journalist_enabled_at || Time.zone.parse('09-06-2022')

    SingularDatum.
      includes(:user).
      where(invited_by: id).
      where('users.created_at >= ?', marker_date).
      order('users.created_at DESC').
      map do |dd|
      u = dd.user

      is_new_install = dd.created_at.to_date == u.created_at.to_date
      is_leader = u.get_badge_role.present?
      signup_type_sym = if is_leader
                          is_new_install ? :leader_new_install : :leader_re_install
                        else
                          is_new_install ? :normal_new_install : :normal_re_install
                        end

      {
        name: u.name,
        signup_date: u.created_at.strftime('%d %b, %Y'),
        signup_type_sym: signup_type_sym,
        signup_type: get_signup_type_text(signup_type_sym),
        points: get_signup_type_points(signup_type_sym),
        phone: u.phone.to_s.tap { |p| p[2...7] = 'XXXXX' }
      }
    end
  end

  def get_signup_type_points(signup_type_sym)
    case signup_type_sym
    when :leader_new_install
      400
    when :leader_re_install
      150
    when :normal_new_install
      150
    when :normal_re_install
      0
    else
      # type code here
    end
  end

  def get_signup_type_text(signup_type_sym)
    case signup_type_sym
    when :leader_new_install
      'నాయకుడు ఇన్స్టాల్'
    when :leader_re_install
      'నాయకుడు రీ-ఇన్స్టాల్'
    when :normal_new_install
      'సాధారణ ఇన్స్టాల్'
    when :normal_re_install
      'సాధారణ రీ-ఇన్స్టాల్'
    else
      # type code here
    end
  end

  def get_referral_summary(data = nil)
    summary_data = get_referral_summary_data(data)

    {
      data: summary_data,
      total_points_earned: get_total_referral_points_earned(summary_data),
      total_points_paid: get_total_referral_points_paid
    }
  end

  def get_referral_summary_data(data = nil)
    data = get_referral_data if data.nil?

    grouped_data = data.group_by { |v| v[:signup_type_sym] }

    summary_data = []
    grouped_data.each do |signup_type, group_data|
      summary_data << {
        signup_type: get_signup_type_text(signup_type),
        user_count: group_data.count,
        points_per_user: get_signup_type_points(signup_type),
        points: group_data.sum { |g| g[:points] }
      }
    end

    summary_data
  end

  def get_total_referral_points_earned(summary_data = nil)
    summary_data = get_referral_summary_data if summary_data.nil?

    summary_data.sum { |d| d[:points] }
  end

  def get_total_referral_points_paid
    self.user_points_ledgers.debit.sum(&:points)
  end

  def get_followers(signed_in_user, offset = 0, count = 0)
    user_followers = if offset.zero? && count.zero?
                       UserFollower.active_followers.where(user: self)
                     else
                       UserFollower.active_followers.where(user: self).offset(offset).limit(count)
                     end

    user_followers.map do |user_follower|
      follower = user_follower.follower
      follower.badge = follower.get_badge_role&.get_json
      unless signed_in_user.nil?
        follower.phone = follower.generate_random_phone
        follower.follows = (follower.id == signed_in_user.id ? false : UserFollower.where(user: follower, follower: signed_in_user).exists?)
        follower.loggedInUser = (follower.id == signed_in_user.id)
        follower.photo&.compressed_url!(size: 200)
      end
      follower
    end
  end

  def followers_count
    total_followers_count
    # followers.count
  end

  def get_following(signed_in_user, offset = 0, count = 0)
    user_following = if offset.zero? && count.zero?
                       UserFollower.active_following.where(follower: self)
                     else
                       UserFollower.active_following.where(follower: self).offset(offset).limit(count)
                     end

    user_following.map do |user_following|
      following = user_following.user
      following.badge = following.get_badge_role&.get_json
      unless signed_in_user.nil?
        following.phone = following.generate_random_phone
        following.follows = (following.id == signed_in_user.id ? false : UserFollower.where(user: following, follower: signed_in_user).exists?)
        following.loggedInUser = (following.id == signed_in_user.id)
        following.photo&.compressed_url!(size: 200)
      end
      following
    end
  end

  def following_count
    total_following_count
    # UserFollower.where(follower: self, active: true).count
  end

  def get_posts_count(include_inactive = false)
    if include_inactive
      Post.where(user: self).count
    else
      Post.where(user: self, active: true).count
    end
  end

  def get_likes_count
    Post.joins(:post_likes).where(posts: { active: true }, post_likes: { user: self, active: true }).count
  end

  # not using this method as of now
  # def get_mentioned_posts_count
  #   Post.joins(:post_user_tag).where(posts: { active: true }, post_user_tags: { user: self, active: true }).count
  # end

  def user_json_for_members_tab(user, followed_user_ids)
    self.photo&.compressed_url!(size: 200)
    # TODO: need to modify badge parameter once moderator,admin roles.. has been introduced in circles.
    {
      id: self.id,
      name: self.name,
      phone: self.generate_random_phone, # self.phone,
      photo: self.photo,
      village: User.core_party_user_ids.include?(self.id) ? nil : { "id": self.village.id, "name": self.village.name, "name_en": self.village.name_en }, # TODO: remove this hack
      followers_count: self.followers_count,
      loggedInUser: self.id == user.id,
      follows: followed_user_ids.include?(self.id),
      badge: self.get_badge_role&.get_json,
      avatar_color: self.avatar_color,
      short_bio: self.short_bio
    }
  end

  def get_posts(user, offset, count, app_version, include_inactive = false)
    # Start building the query
    query = Post.where(user: self)

    # Conditionally add to the query to exclude inactive posts, if required
    query = query.where(active: true) unless include_inactive

    # Apply ordering
    query = query.order(id: :desc)

    # Apply pagination if offset and count are not zero
    query = query.offset(offset).limit(count) unless offset.zero? && count.zero?

    # At this point, the query has been built but not executed.
    # You can now pass around the 'query' object, and it will only execute when needed.
    # For example:
    posts = query.all

    post_ids = posts.map(&:id)
    liked_post_ids = PostLike.where(user: user, post_id: post_ids, active: true).pluck(:post_id)
    post_likes_count_map = PostLike.where(post_id: post_ids, active: true).group(:post_id).count
    post_comments_count_map = PostComment.where(post_id: post_ids, active: true).group(:post_id).count
    post_opinions_count_map = Post.where(parent_post_id: post_ids, active: true).group(:parent_post_id).count

    posts.map do |p|
      post = Post.get_feed_item(p.id, user, app_version)
      post[:user][:follows] = p.user_id == user.id ? nil : UserFollower.where(user: self, follower: user).exists?
      post[:user_liked] = liked_post_ids.include? p.id

      post[:likes_count] = post_likes_count_map[p.id] || 0
      post[:comments_count] = post_comments_count_map[p.id] || 0
      post[:opinions_count] = post_opinions_count_map[p.id] || 0
      post[:whatsapp_count] = p.whatsapp_count
      post[:preview_comments] = []

      post
    end
  end

  # get_mentioned_posts is not used as of now so removed it

  def get_liked_posts(user, offset, count, app_version)
    if offset.zero? && count.zero?
      posts = Post.joins(:post_likes).where(posts: { active: true }, post_likes: { user: self, active: true }).order('post_likes.id DESC').all
    else
      posts = Post.joins(:post_likes).where(posts: { active: true }, post_likes: { user: self, active: true }).order('post_likes.id DESC').offset(offset).limit(count).all
    end

    post_ids = posts.map(&:id)
    liked_post_ids = []
    if self.id != user.id
      liked_post_ids = PostLike.where(user: user, post_id: post_ids, active: true).pluck(:post_id)
    end
    post_likes_count_map = PostLike.where(post_id: post_ids, active: true).group(:post_id).count
    post_comments_count_map = PostComment.where(post_id: post_ids, active: true).group(:post_id).count
    post_opinions_count_map = Post.where(parent_post_id: post_ids, active: true).group(:parent_post_id).count

    posts.map do |p|
      post = Post.get_feed_item(p.id, user, app_version)
      post[:user][:follows] = p.user_id == user.id ? nil : UserFollower.where(user: self, follower: user).exists?
      post[:user_liked] = self.id == user.id ? true : (liked_post_ids.include? p.id)

      post[:likes_count] = post_likes_count_map[p.id] || 0
      post[:comments_count] = post_comments_count_map[p.id] || 0
      post[:opinions_count] = post_opinions_count_map[p.id] || 0
      post[:whatsapp_count] = p.whatsapp_count
      post[:preview_comments] = []

      post
    end
  end

  def get_unique_otp_with_counter
    if self.phone == Constants.app_store_account_phone || self.phone == Constants.pg_account_phone
      [1, Rails.application.credentials[:app_store_account_otp].to_s]
    elsif is_google_mfa_enabled?
      [1, ROTP::TOTP.new(Rails.application.credentials[:fantom_accounts_mfa_secret]).now.to_s]
    else
      old_code_with_counter = $redis.get(self.otp_key)
      if old_code_with_counter.present?
        old_code_counter_split = old_code_with_counter.split(':')
        counter = old_code_counter_split.first.to_i
        old_code = old_code_counter_split.last.to_i

        counter += 1
        $redis.set(self.otp_key, "#{counter}:#{old_code}")

        return [counter, old_code]
      end

      code = Random.rand(10_000..99_999)
      $redis.set(self.otp_key, "1:#{code}", ex: 10.minutes.to_i)

      [1, code.to_s]
    end
  end

  def get_unique_otp_for_email
    old_otp = $redis.get(self.email_otp_key)
    if old_otp.present?
      old_otp
    else
      otp = Random.rand(10_000..99_999)
      $redis.set(self.email_otp_key, otp.to_s, ex: 10.minutes.to_i)
      otp.to_s
    end
  end

  def send_otp_email(otp)
    from = Email.new(email: '<EMAIL>')
    to = Email.new(email: self.email)
    subject = 'Your OTP for Praja login'
    html_content = ActionController::Base.render(
      inline: File.read(Rails.root.join('app/views/user_mailer/send_otp_via_email.html.erb')),
      locals: { user_name: self.name, otp: otp }
    )
    content = Content.new(type: 'text/html', value: html_content)
    mail = Mail.new(from, subject, to, content)

    sg = SendGrid::API.new(api_key: Rails.application.credentials[:sendgrid_api_key])
    response = sg.client
                 .mail
                 ._('send')
                 .post(request_body: mail.to_json)

    response.status_code.to_i >= 200 && response.status_code.to_i < 300
  end

  def self.get_truecaller_public_key
    'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDEpFwIarbm48m6ueG+jhpt2vCGaqXZlwR/HPuL4zH1DQ/eWFbgQtVnrta8QhQz3ywLnbX6s7aecxUzzNJsTtS8VxKAYll4E1lJUqrNdWt8CU+TaUQuFm8vzLoPiYKEXl4bX5rzMQUMqA228gWuYmRFQnpduQTgnYIMO8XVUQXl5wIDAQAB'

    # requested_url = "https://api4.truecaller.com/v1/key"
    # uri = URI.parse(requested_url)
    #
    # res = Net::HTTP.get(uri)
    #
    # resp_body = JSON.parse(res)
    #
    # return resp_body[0]['key'] if !resp_body.nil? && resp_body.present? && resp_body.count > 0
  end

  def mark_suggested_as_seen(user_id)
    unless $redis.hexists("suggested_user_views_#{self.id}", user_id.to_s)
      $redis.hset("suggested_user_views_#{self.id}", user_id.to_s, Time.now.to_i.to_s)
    end
  end

  def send_otp(code, otp_count, app_hash)
    return nil unless self.should_send_otp?

    SendOtpJob.perform_async(self.id, self.phone, code, otp_count, app_hash)
  end

  def get_feed_circle_suggestions_v2(session_count)
    session_based_circle_suggestions(0, 10, session_count)
  end

  def session_based_circle_suggestions(offset, count, session_count)
    get_suggested_circles_v2(offset, count, session_count)
  end

  def get_feed_circle_suggestions
    user_mandal = village.parent_circle
    user_district = user_mandal.parent_circle

    # Hack to show specific party circles to Telangana & AP users
    circle_ids = [31_403, 31_402, 31_406]
    circle_ids = [31_405, 31_398, 31_401] if user_district.id < 13_279 # It is Telangana

    circle_ids = [] if Rails.env == 'development'

    new_circles = []
    party_joined = nil
    Circle.where(id: circle_ids).each do |circle|
      next unless circle.active

      circle.is_user_joined = circle.check_user_joined(self)

      # If user joins atleast 1 party circle, break the loop with empty suggestions
      if circle.is_user_joined
        new_circles = []
        party_joined = circle
        break
      else
        circle.members_count = circle.get_members_count
        new_circles << circle
      end
    end

    # if @app_version >= Gem::Version.new('1.7.0')
    if new_circles.length <= 0 && !party_joined.nil?
      new_circles = Circle
                      .includes(:users)
                      .where(parent_circle: party_joined,
                             circle_type: :interest,
                             level: :political_leader)
                      .limit(3).each do |circle|
        circle.is_user_joined = circle.check_user_joined(self)
      end
    end

    if new_circles.length <= 0
      new_circles = []
      new_circles << get_mp
      new_circles << get_mla
    end
    # end

    # To remove `nil` cases
    new_circles = new_circles.compact

    skipped_data = $redis.hgetall("circle_skips_#{id}")
    skipped_circle_ids = []
    skipped_circle_ids = skipped_data.map { |id, value| id.to_i } unless skipped_data.nil?

    timeout_data = $redis.zrangebyscore("circle_display_count_#{self.id}", '2', '+inf')
    timeout_circle_ids = []
    timeout_circle_ids = timeout_data.map { |id, value| id.to_i } unless timeout_data.nil?

    new_circles_list = new_circles.map do |circle|
      circle.feed_type = 'new_circle'
      circle.is_user_joined = circle.check_user_joined(self)

      next if timeout_circle_ids.include?(circle.id) || skipped_circle_ids.include?(circle.id)

      next if circle.is_user_joined

      $redis.zincrby("circle_display_count_#{self.id}", 1.0, circle.id)
      circle.photo = Photo.find(circle.photo_id) if !circle.photo_id.nil? && circle.photo_id.positive?
      circle
    end

    # To remove `nil` cases
    new_circles_list.compact
  end

  def should_send_otp?
    Rails.env.production? &&
      self.phone != Constants.app_store_account_phone &&
      self.phone != Constants.pg_account_phone &&
      !is_google_mfa_enabled?
  end

  def is_google_mfa_enabled?
    self.phone == Constants.praja_account_phone || self.phone.to_s.match?(/2[0-9]{9}$/)
  end

  def validate_otp(otp)
    if self.phone == Constants.app_store_account_phone || self.phone == Constants.pg_account_phone
      otp == Rails.application.credentials[:app_store_account_otp].to_s
    elsif self.is_google_mfa_enabled?
      ROTP::TOTP.new(Rails.application.credentials[:fantom_accounts_mfa_secret]).verify(otp, drift_behind: 5).present?
    else
      saved_otp_with_counter = $redis.get(self.otp_key)
      saved_otp = nil
      saved_otp = saved_otp_with_counter.split(':').last if saved_otp_with_counter.present?

      valid = saved_otp.present? && saved_otp == otp

      # Clear OTP cache if validated successfully
      self.clear_otp_cache if valid

      valid
    end
  end

  def clear_otp_cache
    $redis.del(self.otp_key)
  end

  def validate_email_otp(otp)
    return true if email == '<EMAIL>' && otp == 11111

    saved_otp = $redis.get(self.email_otp_key).to_i
    is_valid = saved_otp.present? && saved_otp == otp
    self.clear_email_otp_cache if is_valid
    is_valid
  end

  def clear_email_otp_cache
    $redis.del(self.email_otp_key)
  end

  def generate_jwt_token
    User.generate_user_jwt_token(id)
  end

  def self.generate_user_jwt_token(user_id)
    JsonWebToken.encode({ user_id: user_id, created_at: Time.zone.now.to_i })
  end

  def generate_login_token(app_version, app_os)
    # from the version when JWT is implemented, ignore generating user tokens
    return '' if AppVersionSupport.is_jwt_implemented?
    # UserToken.where(user: self).update_all(active: false, updated_at: Time.now)

    begin
      return UserToken.create!(user: self, app_version: app_version, app_os: app_os).access_token
    rescue => e
      Honeybadger.notify(e)
      return ''
    end
  end

  def self.get_ordered_users_list(logged_in_user, offset, count)
    sql = "SELECT u.id as user_id, (SELECT COUNT(uf.id) FROM user_followers uf where uf.user_id = u.id) as followers_count
          FROM users u
          WHERE u.status = 'active'
          order by 2 DESC, u.created_at DESC
          LIMIT #{offset}, #{count}"

    users = []

    query_results = ActiveRecord::Base.connection.execute(sql)
    query_results.each do |result|
      user = User.find(result[0].to_i)

      user.loggedInUser = (logged_in_user.id == user.id)
      user.follows = user.get_followers(logged_in_user).map(&:id).include?(logged_in_user.id) unless user.loggedInUser
      user.badge = user.get_badge_role&.get_json
      user.photo&.compressed_url!(size: 200)

      users << user
    end

    users
  end

  def get_badge_notification_data(include_unverified_badge: false)
    # This include_unverified_badge is used to show the badge celebration for unverified badges also
    # because app is calling notify badge api for unverified badges also as they don't know whether the badge is
    # verified or not till the app unverified badge user profile share
    # So, we are passing this flag to show the badge celebration for unverified badges also to avoid issues while
    # calling unverified badge user profile share
    badge_user_role = include_unverified_badge ? self.get_badge_role_including_unverified : self.get_badge_role
    return nil if badge_user_role.nil?

    if badge_user_role.verified_verification_status?
      self.badge = badge_user_role.get_json
      badge_role_header = badge_user_role.get_badge_role_header_for_badge_celebration
      sub_header = badge_user_role.get_sub_header_for_badge_celebration
    else
      badge_role_header = ""
      sub_header = ""
    end

    {
      header: 'Praja App లోకి స్వాగతం',
      invite_card_header: 'Praja App లో నన్ను ఫాలో అవ్వండి',
      upload_picture_text: 'ఫోటో జోడించండి',
      badge_role_header: badge_role_header,
      sub_header: sub_header,
      user: self,
      footer: 'Praja లో బ్యాడ్జి గుర్తింపును మీ పరిచయస్తులతో పంచుకోండి',
    }
  end

  def get_dynamic_link
    if dynamic_link.nil? || dynamic_link.empty?
      parameters = {
        link: "https://m.praja.buzz/?invited_by=#{hashid}",
        androidInfo: {
          androidPackageName: 'buzz.praja.app',
          androidMinPackageVersionCode: '56'
        },
        iosInfo: {
          iosBundleId: 'buzz.praja.app',
          iosAppStoreId: '1498121369',
          iosCustomScheme: 'praja'
        },
        socialMetaTagInfo: {
          socialTitle: 'Praja App – Telugu Local Trends, Political Circles',
          socialDescription: 'Find the most trending content (news, jobs, politics) around you. Join your local politician circles. Best online political network. The made in India social network for Indian users.',
          socialImageLink: 'https://firebasestorage.googleapis.com/v0/b/circle-777ed.appspot.com/o/FCMImages%2Flogo-transparent.png?alt=media&token=f28e7623-5246-49ce-b27a-52b9897d233c'
        }
      }

      result = $firebase_dl_client.shorten_parameters(parameters)

      if !result[:link].nil? && result[:link] != ''
        self.dynamic_link = result[:link]
        save
      else
        self.dynamic_link = ''
      end
    end

    link = 'http://onelink.to/praja-buzz'
    link = dynamic_link if dynamic_link != ''

    link
  end

  def get_profile_share_link
    if profile_dynamic_link.nil? || profile_dynamic_link.empty?
      parameters = {
        link: "https://m.praja.buzz/users/#{hashid}",
        androidInfo: {
          androidPackageName: 'buzz.praja.app',
          androidMinPackageVersionCode: '56'
        },
        iosInfo: {
          iosBundleId: 'buzz.praja.app',
          iosAppStoreId: '1498121369',
          iosCustomScheme: 'praja'
        },
        socialMetaTagInfo: {
          socialTitle: 'Praja App – Telugu Local Trends, Political Circles',
          socialDescription: 'Find the most trending content (news, jobs, politics) around you. Join your local politician circles. Best online political network. The made in India social network for Indian users.',
          socialImageLink: 'https://firebasestorage.googleapis.com/v0/b/circle-777ed.appspot.com/o/FCMImages%2Flogo-transparent.png?alt=media&token=f28e7623-5246-49ce-b27a-52b9897d233c'
        }
      }

      result = $firebase_dl_client.shorten_parameters(parameters)

      if !result[:link].nil? && result[:link] != ''
        self.profile_dynamic_link = result[:link]
        save
      else
        self.profile_dynamic_link = ''
      end
    end

    link = 'http://onelink.to/praja-buzz'
    link = profile_dynamic_link if profile_dynamic_link != ''

    link
  end

  def get_mla
    return if mla_constituency_id.nil?

    mla_relation = CirclesRelation.where(first_circle_id: mla_constituency_id, relation: 'MLA').first
    mla = mla_relation.second_circle unless mla_relation.nil?
    mla.is_user_joined = mla.check_user_joined(self) unless mla.nil?

    mla
  end

  def get_mp
    return if mp_constituency_id.nil?

    mp_relation = CirclesRelation.where(first_circle_id: mp_constituency_id, relation: 'MP').first
    mp = mp_relation.second_circle unless mp_relation.nil?
    mp.is_user_joined = mp.check_user_joined(self) unless mp.nil?

    mp
  end

  def get_user_political_parties
    fetch_suggested_circles = []

    state_level_political_party_ids = []
    state_id = self.state_id
    if state_id.present?
      state_level_political_party_ids = CirclesRelation.where(active: true, second_circle_id: state_id, relation: 'Party2State')
                                                       .all
                                                       .map(&:first_circle).map(&:id)
    end

    c_score_column = '0'
    c_score_column = "IF(FIND_IN_SET(id,'#{state_level_political_party_ids.join(',')}'),0,1)" if state_level_political_party_ids.present?

    get_user_circles = circles.ids
    get_circles = Circle.where('level = ? AND active is true', 6)
                        .select("*, #{c_score_column} AS score, members_count AS user_count")
                        .group('id')
                        .order('level ASC, score ASC, user_count DESC')

    get_circles.each do |c|
      unless get_user_circles.include? c.id
        c.is_user_joined = false
        fetch_suggested_circles << c
      end
    end

    fetch_suggested_circles
  end

  # political party circle ids with score
  def party_ids_with_score(user_joined_interest_circle_ids, score)
    party_ids_data = CirclesRelation.select(:first_circle_id, :relation,
                                            Arel.sql("(CASE
       WHEN second_circle_id = #{state_id} AND relation = 'Party2State' THEN #{score}
       WHEN second_circle_id != #{state_id} AND relation = 'Party2State' THEN #{score += 1}
       END) AS score"))
                                    .where(
                                      relation: [:Party2State],
                                      active: true
                                    )
                                    .where.not(first_circle_id: user_joined_interest_circle_ids)
                                    .order(:score)
    # ignore if score is nil and don't add to hash
    party_ids_with_score = {}
    unless party_ids_data.empty?
      party_ids_data.each do |data|
        next if data.score.nil? || party_ids_with_score.key?(data.first_circle_id)
        party_ids_with_score[data.first_circle_id] = data.score
      end
    end
    [party_ids_with_score, score]
  end

  # state level leader circle ids with score for party affiliated user
  def state_level_leader_ids_with_score_for_affiliated_user(user_joined_political_party_ids,
                                                            user_joined_interest_circle_ids,
                                                            suggested_list_hash_with_score,
                                                            score)
    state_level_leader_ids_with_score = {}
    state_level_leader_ids_data = CirclesRelation
                                    .joins('INNER JOIN circles_relations cr2 ON circles_relations.first_circle_id = cr2.first_circle_id')
                                    .select(:first_circle_id, :relation,
                                            Arel.sql("(CASE
            WHEN circles_relations.second_circle_id = #{state_id} AND circles_relations.relation = 'Leader2State'
            THEN #{score += 1}
            END) AS score"))
                                    .where(
                                      second_circle_id: state_id,
                                      active: true,
                                      relation: 'Leader2State',
                                      cr2: {
                                        relation: 'Leader2Party',
                                        second_circle_id: user_joined_political_party_ids,
                                        active: true
                                      }
                                    )
                                    .where.not(first_circle_id: user_joined_interest_circle_ids +
      suggested_list_hash_with_score.keys)
                                    .order(:score)
    unless state_level_leader_ids_data.empty?
      # ignore if score is nil and don't add to hash
      state_level_leader_ids_data.each do |data|
        next if data.score.nil? || state_level_leader_ids_with_score.key?(data.first_circle_id)
        state_level_leader_ids_with_score[data.first_circle_id] = data.score
      end
    end
    [state_level_leader_ids_with_score, score]
  end

  # MP and MLA circle ids irrespective of party for party affiliated user
  def mp_and_mla_with_score_for_affiliated_user(user_joined_interest_circle_ids, suggested_list_hash_with_score, score)
    mp_mla_ids_with_score = {}
    mp_mla_ids_data = CirclesRelation
                        .select(:second_circle_id,
                                Arel.sql("(CASE
            WHEN first_circle_id =#{mp_constituency_id} AND relation = 'MP' THEN #{score += 1}
            WHEN first_circle_id =#{mla_constituency_id} AND relation = 'MLA' THEN #{score += 1}
            END) AS score"))
                        .where(
                          first_circle_id: [mp_constituency_id, mla_constituency_id],
                          relation: [:MP, :MLA, :MP_Contestant, :MLA_Contestant],
                          active: true
                        )
                        .where.not(second_circle_id: user_joined_interest_circle_ids +
      suggested_list_hash_with_score.keys)
                        .order(:score)
    unless mp_mla_ids_data.empty?
      # ignore if score is nil and don't add to hash
      mp_mla_ids_data.each do |data|
        next if data.score.nil? || mp_mla_ids_with_score.key?(data.second_circle_id)
        mp_mla_ids_with_score[data.second_circle_id] = data.score
      end
    end
    [mp_mla_ids_with_score, score]
  end

  # local level leader circle ids with score for party affiliated user
  def local_leader_circle_ids_with_score_for_affiliated_user(user_joined_political_party_ids, user_joined_interest_circle_ids,
                                                             suggested_list_hash_with_score, score)
    local_leader_circle_ids_with_score = {}
    user_mp_constituency_child_circle_ids = mp_constituency.get_all_child_circle_ids_including_itself
    # form hash with circle id as key and score as value
    local_leader_circle_ids_data = CirclesRelation.joins('INNER JOIN circles_relations cr2 ON circles_relations.second_circle_id = cr2.first_circle_id')
                                                  .select(:second_circle_id,
                                                          Arel.sql("(CASE
            WHEN circles_relations.first_circle_id =#{mp_constituency_id} AND circles_relations.relation = 'MP Contestant' THEN #{score += 1}
            WHEN circles_relations.first_circle_id =#{mla_constituency_id} AND circles_relations.relation = 'MLA Contestant' THEN #{score += 1}
            WHEN circles_relations.first_circle_id IN (#{user_mp_constituency_child_circle_ids.join(',')})
            AND circles_relations.relation = 'MLA' THEN #{score += 1}
            WHEN circles_relations.first_circle_id IN (#{user_mp_constituency_child_circle_ids.join(',')}) AND
            circles_relations.relation = 'MLA Contestant' THEN #{score += 1}
            END) AS score"))
                                                  .where(
                                                    first_circle_id: user_mp_constituency_child_circle_ids,
                                                    relation: [:MP, :MLA, :MP_Contestant, :MLA_Contestant],
                                                    active: true,
                                                    cr2: {
                                                      relation: 'Leader2Party',
                                                      second_circle_id: user_joined_political_party_ids,
                                                      active: true
                                                    }
                                                  )
                                                  .where.not(second_circle_id: user_joined_interest_circle_ids +
      suggested_list_hash_with_score.keys)
                                                  .order(:score)
    unless local_leader_circle_ids_data.empty?
      # ignore if score is nil and don't add to hash
      local_leader_circle_ids_data.each do |data|
        next if data.score.nil? || local_leader_circle_ids_with_score.key?(data.second_circle_id)
        local_leader_circle_ids_with_score[data.second_circle_id] = data.score
      end
    end
    [local_leader_circle_ids_with_score, score]
  end

  # district level leader circle ids with score for party affiliated user
  def district_level_leader_circle_ids_with_score_for_affiliated_user(user_joined_political_party_ids,
                                                                      user_joined_interest_circle_ids,
                                                                      suggested_list_hash_with_score, score)
    district_level_leader_circle_ids_with_score = {}
    user_district_mandals = []
    user_district_mandals = district.child_circles unless district.nil?
    user_district_mla_constituencies = CirclesRelation.where(first_circle: user_district_mandals.map do |c|
      c if c.id != mandal_id
    end, active: true, relation: 'Mandal2MLA').all.map(&:second_circle)

    user_district_mla_constituency_ids = []
    user_district_mla_constituencies.each do |c|
      user_district_mla_constituency_ids << c.id if c.id != mla_constituency_id
    end

    user_district_mp_constituency_ids = []
    user_district_mla_constituencies.each do |c|
      user_district_mp_constituency_ids << c.parent_circle.id if c.parent_circle_id != mp_constituency_id
    end

    if user_district_mp_constituency_ids.present?
      mp_constituency_query = "WHEN circles_relations.first_circle_id IN (#{user_district_mp_constituency_ids.join(',')})
                                                  AND circles_relations.relation = 'MP' THEN #{score += 1}
      WHEN circles_relations.first_circle_id IN (#{user_district_mp_constituency_ids.join(',')}) AND
                                                  circles_relations.relation = 'MP Contestant' THEN #{score += 1}"
    else
      mp_constituency_query = ''
    end

    district_level_leader_circle_ids_data = CirclesRelation.joins("INNER JOIN circles_relations cr2 ON
             circles_relations.second_circle_id = cr2.first_circle_id")
                                                           .select(:second_circle_id,
                                                                   Arel.sql("(CASE
            #{mp_constituency_query}
            WHEN circles_relations.first_circle_id IN (#{user_district_mla_constituency_ids.join(',')}) AND
            circles_relations.relation = 'MLA' THEN #{score += 1}
            WHEN circles_relations.first_circle_id IN (#{user_district_mla_constituency_ids.join(',')}) AND
            circles_relations.relation = 'MLA Contestant' THEN #{score += 1}
            WHEN circles_relations.first_circle_id =#{district_id} AND circles_relations.relation = 'MP Rajyasabha'
                                                                                                  THEN #{score += 1}
            WHEN circles_relations.first_circle_id =#{district_id} AND circles_relations.relation = 'MLC'
                                                                                                  THEN #{score += 1}
            WHEN circles_relations.first_circle_id =#{district_id} AND circles_relations.relation = 'MLC Contestant'
                                                                                                  THEN #{score += 1}
            END) AS score"))
                                                           .where(
                                                             first_circle_id:
                                                               user_district_mp_constituency_ids +
                                                                 user_district_mla_constituency_ids + [district_id],
                                                             relation: [:MP, :MLA, :MP_Contestant, :MLA_Contestant, :MP_Rajyasabha,
                                                                        :MLC, :MLC_Contestant],
                                                             active: true,
                                                             cr2: {
                                                               relation: 'Leader2Party',
                                                               second_circle_id: user_joined_political_party_ids,
                                                               active: true
                                                             }
                                                           )
                                                           .where.not(second_circle_id:
                                                                        user_joined_interest_circle_ids +
                                                                          suggested_list_hash_with_score.keys)
                                                           .order(:score)
    unless district_level_leader_circle_ids_data.empty?
      # ignore if score is nil and don't add to hash
      district_level_leader_circle_ids_data.each do |data|
        next if data.score.nil? || district_level_leader_circle_ids_with_score.key?(data.second_circle_id)
        district_level_leader_circle_ids_with_score[data.second_circle_id] = data.score
      end
    end
    [district_level_leader_circle_ids_with_score, score]
  end

  # party and state leader circle ids with score
  def party_and_state_leader_ids_with_score(user_joined_interest_circle_ids, score)
    party_and_state_leader_ids_data = CirclesRelation.select(:first_circle_id, :relation,
                                                             Arel.sql("(CASE
       WHEN second_circle_id = #{state_id} AND relation = 'Party2State' THEN #{score}
       WHEN second_circle_id != #{state_id} AND relation = 'Party2State' THEN #{score += 1}
       WHEN second_circle_id = #{state_id} AND relation = 'Leader2State' THEN #{score += 1}
       WHEN second_circle_id != #{state_id} AND relation = 'Leader2State' THEN #{score += 1}
       END) AS score"))
                                                     .where(
                                                       relation: [:Party2State, :Leader2State],
                                                       active: true
                                                     )
                                                     .where.not(first_circle_id: user_joined_interest_circle_ids)
                                                     .order(:score)
    party_and_state_leader_ids_with_score = {}
    unless party_and_state_leader_ids_data.empty?
      # ignore if score is nil and don't add to hash
      party_and_state_leader_ids_data.each do |data|
        next if data.score.nil? || party_and_state_leader_ids_with_score.key?(data.first_circle_id)
        party_and_state_leader_ids_with_score[data.first_circle_id] = data.score
      end
    end
    [party_and_state_leader_ids_with_score, score]
  end

  # local level leader circle ids with score
  def local_leader_circle_ids_with_score(user_joined_interest_circle_ids, suggested_list_hash_with_score, score)
    local_leader_circle_ids_with_score = {}
    user_mp_constituency_child_circle_ids = mp_constituency.get_all_child_circle_ids_including_itself
    # form hash with circle id as key and score as value
    local_leader_circle_ids_data = CirclesRelation
                                     .select(:second_circle_id,
                                             Arel.sql("(CASE
            WHEN first_circle_id =#{mp_constituency_id} AND relation = 'MP' THEN #{score += 1}
            WHEN first_circle_id =#{mla_constituency_id} AND relation = 'MLA' THEN #{score += 1}
            WHEN first_circle_id =#{mp_constituency_id} AND relation = 'MP Contestant' THEN #{score += 1}
            WHEN first_circle_id =#{mla_constituency_id} AND relation = 'MLA Contestant' THEN #{score += 1}
            WHEN first_circle_id IN (#{user_mp_constituency_child_circle_ids.join(',')})
            AND relation = 'MLA' THEN #{score += 1}
            WHEN first_circle_id IN (#{user_mp_constituency_child_circle_ids.join(',')}) AND
            relation = 'MLA Contestant' THEN #{score += 1}
            END) AS score"))
                                     .where(
                                       first_circle_id: user_mp_constituency_child_circle_ids,
                                       relation: [:MP, :MLA, :MP_Contestant, :MLA_Contestant],
                                       active: true
                                     )
                                     .where.not(second_circle_id: user_joined_interest_circle_ids +
      suggested_list_hash_with_score.keys)
                                     .order(:score)
    unless local_leader_circle_ids_data.empty?
      # ignore if score is nil and don't add to hash
      local_leader_circle_ids_data.each do |data|
        next if data.score.nil? || local_leader_circle_ids_with_score.key?(data.second_circle_id)
        local_leader_circle_ids_with_score[data.second_circle_id] = data.score
      end
    end
    [local_leader_circle_ids_with_score, score]
  end

  # district level leader circle ids with score
  def district_leader_circle_ids_with_score(user_joined_interest_circle_ids, suggested_list_hash_with_score, score)
    district_level_leader_circle_ids_with_score = {}
    user_district_mandals = []
    user_district_mandals = district.child_circles unless district.nil?
    user_district_mla_constituencies = CirclesRelation.where(first_circle: user_district_mandals.map do |c|
      c if c.id != mandal_id
    end, active: true, relation: 'Mandal2MLA').all.map(&:second_circle)

    user_district_mla_constituency_ids = []
    user_district_mla_constituencies.each do |c|
      user_district_mla_constituency_ids << c.id if c.id != mla_constituency_id
    end

    user_district_mp_constituency_ids = []
    user_district_mla_constituencies.each do |c|
      user_district_mp_constituency_ids << c.parent_circle.id if c.parent_circle_id != mp_constituency_id
    end

    if user_district_mp_constituency_ids.present?
      mp_constituency_query = "WHEN first_circle_id IN (#{user_district_mp_constituency_ids.join(',')})
                                                        AND relation = 'MP' THEN #{score += 1}
      WHEN first_circle_id IN (#{user_district_mp_constituency_ids.join(',')}) AND
                                relation = 'MP Contestant' THEN #{score += 1}"
    else
      mp_constituency_query = ''
    end
    district_level_leader_circle_ids_data = CirclesRelation
                                              .select(:second_circle_id,
                                                      Arel.sql("(CASE
            #{mp_constituency_query}
            WHEN first_circle_id IN (#{user_district_mla_constituency_ids.join(',')}) AND
            relation = 'MLA' THEN #{score += 1}
            WHEN first_circle_id IN (#{user_district_mla_constituency_ids.join(',')}) AND
            relation = 'MLA Contestant' THEN #{score += 1}
            WHEN first_circle_id =#{district_id} AND relation = 'MP Rajyasabha' THEN #{score += 1}
            WHEN first_circle_id =#{district_id} AND relation = 'MLC' THEN #{score += 1}
            WHEN first_circle_id =#{district_id} AND relation = 'MLC Contestant' THEN #{score += 1}
            END) AS score"))
                                              .where(
                                                first_circle_id:
                                                  user_district_mp_constituency_ids +
                                                    user_district_mla_constituency_ids + [district_id],
                                                relation: [:MP, :MLA, :MP_Contestant, :MLA_Contestant, :MP_Rajyasabha,
                                                           :MLC, :MLC_Contestant],
                                                active: true
                                              )
                                              .where.not(second_circle_id:
                                                           user_joined_interest_circle_ids +
                                                             suggested_list_hash_with_score.keys)
                                              .order(:score)
    unless district_level_leader_circle_ids_data.empty?
      # ignore if score is nil and don't add to hash
      district_level_leader_circle_ids_data.each do |data|
        next if data.score.nil? || district_level_leader_circle_ids_with_score.key?(data.second_circle_id)
        district_level_leader_circle_ids_with_score[data.second_circle_id] = data.score
      end
    end
    [district_level_leader_circle_ids_with_score, score]
  end

  # using this function for both recommendation circles tab and session based circle suggestions
  def get_suggested_circles_v2(offset, count, session_count = nil)
    if village_id.nil? || mandal_id.nil? || district_id.nil? || state_id.nil?
      Honeybadger.notify("User #{self.id} has nil values for village_id, mandal_id, district_id, state_id")
      return []
    end

    mla_mp_constituencies_data_present = (mla_constituency_id.present? && mp_constituency_id.present?) ? true : false

    user_joined_interest_circle_ids = get_user_joined_interest_circle_ids
    user_joined_political_party_ids = get_user_joined_party_circle_ids

    total_count = offset + count
    score = 0
    suggested_list_hash_with_score = {}
    if user_joined_political_party_ids.length.positive?
      # political parties
      # if user joined a political party then show all parties irrespective of state for recommendation circles tab
      # if user joined a political party then don't show other parties in session based circle suggestions
      if total_count > 0 && session_count.blank?
        party_ids_with_score, score = party_ids_with_score(user_joined_interest_circle_ids, score)
        total_count -= party_ids_with_score.length
        suggested_list_hash_with_score.merge!(party_ids_with_score)
      end

      # state level leaders
      # In session based circle suggestions, show state level leaders only if session_count is <= 2
      if total_count > 0 && (session_count.blank? || session_count <= 2)
        state_level_leader_ids_with_score, score = state_level_leader_ids_with_score_for_affiliated_user(
          user_joined_political_party_ids, user_joined_interest_circle_ids, suggested_list_hash_with_score, score)
        total_count -= state_level_leader_ids_with_score.length

        suggested_list_hash_with_score.merge!(state_level_leader_ids_with_score)
      end

      # MP and MLA irrespective of party
      # In session based circle suggestions, show MP and MLA irrespective of party only if session_count is <= 2
      if mla_mp_constituencies_data_present && total_count > 0 && (session_count.blank? || session_count <= 2)
        mp_mla_ids_with_score, score = mp_and_mla_with_score_for_affiliated_user(user_joined_interest_circle_ids,
                                                                                 suggested_list_hash_with_score, score)
        total_count -= mp_mla_ids_with_score.length

        suggested_list_hash_with_score.merge!(mp_mla_ids_with_score)
      end

      # local level leaders
      # In session based circle suggestions, show local level leaders only if session_count is <= 3
      if mla_mp_constituencies_data_present && total_count > 0 && (session_count.blank? || session_count <= 3)
        local_leader_circle_ids_with_score, score = local_leader_circle_ids_with_score_for_affiliated_user(
          user_joined_political_party_ids, user_joined_interest_circle_ids, suggested_list_hash_with_score, score)
        total_count -= local_leader_circle_ids_with_score.length

        suggested_list_hash_with_score.merge!(local_leader_circle_ids_with_score)
      end

      # get district level leaders
      # In session based circle suggestions, show district level leaders only if session_count is <= 3
      if mla_mp_constituencies_data_present && total_count > 0 && (session_count.blank? || session_count <= 3)
        district_level_leader_circle_ids_with_score, score = district_level_leader_circle_ids_with_score_for_affiliated_user(
          user_joined_political_party_ids, user_joined_interest_circle_ids, suggested_list_hash_with_score, score)
        total_count -= district_level_leader_circle_ids_with_score.length

        suggested_list_hash_with_score.merge!(district_level_leader_circle_ids_with_score)
      end

    else
      # in session based circle suggestions, show party and state leaders only if session_count is <= 1
      if total_count > 0 && (session_count.blank? || session_count == 1)
        party_and_state_leader_ids_with_score, score = party_and_state_leader_ids_with_score(user_joined_interest_circle_ids, score)
        total_count -= party_and_state_leader_ids_with_score.length
        suggested_list_hash_with_score.merge!(party_and_state_leader_ids_with_score)
      end
      # local level leaders
      # In session based circle suggestions, show local level leaders only if session_count is <= 2
      if mla_mp_constituencies_data_present && total_count > 0 && (session_count.blank? || session_count <= 2)
        local_leader_circle_ids_with_score, score = local_leader_circle_ids_with_score(user_joined_interest_circle_ids,
                                                                                       suggested_list_hash_with_score, score)
        total_count -= local_leader_circle_ids_with_score.length

        suggested_list_hash_with_score.merge!(local_leader_circle_ids_with_score)
      end
      # get district level leaders
      # In session based circle suggestions, show district level leaders only if session_count is <= 3
      if mla_mp_constituencies_data_present && total_count > 0 && (session_count.blank? || session_count <= 3)
        district_level_leader_circle_ids_with_score, score = district_leader_circle_ids_with_score(
          user_joined_interest_circle_ids, suggested_list_hash_with_score, score)
        total_count -= district_level_leader_circle_ids_with_score.length

        suggested_list_hash_with_score.merge!(district_level_leader_circle_ids_with_score)
      end
    end
    # remaining leader circles are sorted based on members count (DESC) and score (ASC)
    remaining_leader_circle_ids_data = {}
    if total_count > 0
      score += 1
      remaining_leader_circle_ids = Circle.where(level: :political_leader, active: true)
                                          .where.not(id: suggested_list_hash_with_score.keys + user_joined_interest_circle_ids)
                                          .order(members_count: :desc)
                                          .limit(total_count).pluck(:id)
      remaining_leader_circle_ids.each do |id|
        remaining_leader_circle_ids_data[id] = score
      end
      suggested_list_hash_with_score.merge!(remaining_leader_circle_ids_data)
    end
    fetch_suggested_circles = []
    circles = Circle.where(id: suggested_list_hash_with_score.keys)
    # Sort the circles based on members count (DESC) and score (ASC)

    sorted_circles = circles.sort_by do |circle|
      [suggested_list_hash_with_score[circle.id], -circle.members_count]
    end

    sorted_circles = sorted_circles[offset..(offset + count - 1)] || []
    sorted_circles.each do |circle|
      circle.is_user_joined = false
      circle.photo&.compressed_url!(size: 200)
      circle.circle_banner = circle.get_circle_banner
      fetch_suggested_circles << circle
    end
    fetch_suggested_circles
  end

  def self.is_internal(phone)
    (phone == 9_999_999_999 ||
      phone == Constants.app_store_account_phone ||
      phone == Constants.pg_account_phone ||
      /[1-5]\d{9}$/.match?(phone.to_s))
  end

  def self.get_random_name
    names_data = CSV.read(
      'app/names.csv',
      { headers: :first_row, skip_blanks: true }
    )
    surnames_data = CSV.read(
      'app/surnames.csv',
      { headers: :first_row, skip_blanks: true }
    )

    names_list = []
    names_data.each do |row|
      names_list << {
        name_en: row['NAME_EN'],
        name_te: row['NAME_TE']
      }
    end

    surnames_list = []
    surnames_data.each do |row|
      surnames_list << {
        name_en: row['name_en'],
        name_te: row['name_te']
      }
    end

    # 1 - 7 = in telugu; 8 - 10 = in english;
    rand_lang = rand(1..10)
    # false = telugu; true - english
    lang = rand_lang > 7

    # 0 - name & surname start w/ caps; 1 - all smalls
    caps_type = rand(0..1)

    # 0 - add surname as prefix; 1 - append surname to name; 2 - ignore surname
    surname_prefix = rand(0..2)

    name_entity = names_list.sample
    surname_entity = surnames_list.sample

    if lang
      name = name_entity[:name_en].strip
      surname = surname_entity[:name_en].strip
    else
      name = name_entity[:name_te].strip
      surname = surname_entity[:name_te].strip
    end

    if caps_type == 1
      name = name.downcase
      surname = surname.downcase
    elsif caps_type.zero?
      name = name.capitalize
      surname = surname.capitalize
    end

    full_name = ''
    if surname_prefix.zero?
      full_name = "#{surname} #{name}"
    elsif surname_prefix == 1
      full_name = "#{name} #{surname}"
    elsif surname_prefix == 2
      full_name = name
    end

    full_name
  end

  def follow(follower_id, source_of_follow)
    is_blocked = BlockedUser.where(user_id: [follower_id, self.id], blocked_user_id: [follower_id, self.id]).exists?
    return false if is_blocked

    already_followed = UserFollower.where(user_id: self.id, follower_id: follower_id).exists?
    return true if already_followed

    begin
      UserFollower.create!(user_id: self.id, follower_id: follower_id, source_of_follow: source_of_follow&.to_sym)
      true
    rescue => e
      false
    end
  end

  # @deprecated
  # Please refer to the method `search_data` in UserSearchConcern
  def search_entity_obj
    user_role = self.get_badge_role
    badge_present = user_role.present?

    {
      "name": name,
      "photo_url": photo&.url,
      "village_id": village_id.to_i,
      "mandal_id": mandal_id.to_i,
      "mla_constituency_id": mla_constituency_id.to_i,
      "mp_constituency_id": mp_constituency_id.to_i,
      "district_id": district_id.to_i,
      "state_id": state_id.to_i,
      "phone": phone.to_s,
      "followers_count": followers_count,
      "active": active,
      "status": status,
      "badge": badge_present ? user_role.id : 0,
      "affiliated_political_party_id": get_badge_affiliated_party_circle_id,
      "badge_location_circle_id": get_affiliated_location_circle_id,
      "badge_grade_level": badge_present ? user_role.get_readable_grade_level : 1000,
      "circle_ids": user_circles.map(&:circle_id),
      "badge_description": badge_present ? user_role.get_description : "",
      "badge_description_en": badge_present ? user_role.name_en : "",
      "badge_description_te": badge_present ? user_role.name_te : ""
    }
  end

  def self.posts_count_with_10_trends_in_last_day(user_id)
    posts_hash = PostLike.
      joins(:post).
      where(active: true, posts: { user_id: user_id, active: true, created_at: 1.day.ago.. }).
      group(:post_id).
      having('COUNT(post_likes.id) > 10').
      count
    posts_hash.count
  end

  def saw_trend_tutorial?
    $redis.exists("saw_trend_tutorial_#{id}") == 1
  end

  def should_show_trend_tutorial?
    trend_tutorial_view_count < 3
  end

  def trend_tutorial_view_count
    $redis.hget('saw_trend_tutorial_counter', "#{id}").to_i
  end

  def mark_as_seen_trend_tutorial
    # Expire after 90 days
    $redis.set("saw_trend_tutorial_#{id}", Time.zone.now.to_i, ex: (90 * 24 * 60 * 60), nx: true)

    # A counter of trend tutorial seen
    $redis.hincrby('saw_trend_tutorial_counter', "#{id}", 1)
  end

  def index_for_search(manual = false)
    if active_status? && (manual || saved_change_to_attribute?(:total_followers_count) || saved_change_to_attribute?(:name) || saved_change_to_attribute?(:photo_id))
      # is_badge_user = self.get_badge_role.present?
      IndexSearchEntity.perform_async('user', id)
    end
  end

  def user_exclusive_political_party
    affiliated_political_party_id = get_badge_affiliated_party_circle_id
    return affiliated_political_party_id if affiliated_political_party_id != 0

    # fetch from user meta data if user is not affiliated to any party
    affiliated_political_party_id = get_poster_affiliated_party_id
    return affiliated_political_party_id if affiliated_political_party_id.present?

    user_political_circle_ids = circles.where(circles: { level: :political_party }).ids
    return user_political_circle_ids.first if user_political_circle_ids.count == 1
  end

  def political_leader_circle_ids
    circles.where(circles: { level: :political_leader }).ids
  end

  def user_active_poster(app_version)
    # Get the user's active poster
    # Check if already eligible for posters
    # unless $redis.sismember('enable_posters', id.to_s)
    #   session_count = UserTokenUsage.
    #     joins(:user_token).
    #     where(user_tokens: { user_id: self.id }).
    #     count
    #
    #   return if session_count <= 1
    #
    #   $redis.sadd('enable_posters', id.to_s)
    # end

    # get affiliated circle from user_role if user_role is present else get from user affiliated_party_circle_id column
    user_role = self.get_badge_role
    if user_role.present?
      party_circle_id = user_role.get_badge_user_affiliated_party_circle_id
    else
      party_circle_id = self.affiliated_party_circle_id
    end

    leader_circle_ids = political_leader_circle_ids

    circle_ids = [0]
    circle_ids << party_circle_id if party_circle_id.present?
    circle_ids << village_id
    circle_ids << mandal_id
    circle_ids << district_id
    circle_ids << state_id

    circle_ids += leader_circle_ids if leader_circle_ids.present?
    circle_ids = circle_ids.compact

    # TODO: handle poster enabled for multiple leader circles at one time
    # Ordered by party, leader, village, mandal, district, state & neutral poster
    if AppVersionSupport.has_frame_poster? && has_profile_picture
      poster_type_query = ''
    else
      poster_type_query = "AND posters.poster_type = 'normal'"
    end
    p = Poster.joins(:circle)
              .where("? > start_time AND ? < end_time AND posters.active is true #{poster_type_query} and circle_id IN (#{circle_ids.join(',')})",
                     Time.zone.now,
                     Time.zone.now)
              .select("posters.id, CASE WHEN circles.level = 6 THEN 0 WHEN circles.level = 7 THEN 1 WHEN circles
              .level IN (1,13,14) THEN 2 WHEN circles.level = 2 THEN 3 WHEN circles.level = 3 THEN 4 WHEN circles.level = 10
               THEN 5 ELSE 6 END AS circle_level_order")
              .order('posters.poster_type ASC, circle_level_order ASC')
              .first

    Poster.find(p[:id]) if p.present?
  end

  def info_pop_up
    result = $redis.get("info_pop_up_user_#{id}").to_i
    return result <= 2
  end

  def alert_pop_up
    result = $redis.get("alert_pop_up_user_#{id}").to_i
    return result <= 2
  end

  def check_name_after_trim_white_space
    errors.add(:name, 'మీ పేరు ఖాళీ గా ఉండకూడదు') if self.name&.strip&.length == 0
  end

  def user_circle_posts_count(circle_id)
    posts_posted_in_circle_by_user = Post.joins('INNER JOIN post_circles pc ON pc.post_id = posts.id')
                                         .where('pc.circle_id = ? AND pc.active=1 AND posts.active = 1', circle_id)
                                         .where('posts.user_id = ?', self.id).count
    posts_posted_in_circle_by_user
  end

  def get_user_invitee_count
    SingularDatum.where(invited_by: id).where('created_at > ?', Time.zone.parse(BADGE_CARD_ENABLED_DATE)).count
  end

  def is_eligible_for_badge_card
    political_party_id = self.user_exclusive_political_party
    circle_id_has_badge_icon = BadgeIconGroup.where(circle_id: political_party_id).exists? if political_party_id.present?
    if circle_id_has_badge_icon && Circle::PARTY_GRADIENTS[political_party_id].present?
      political_party_circle = Circle.find(political_party_id)
      check_quota_limit = political_party_circle.check_quota_limit_of_role_in_circle(Constants.get_vip_status_badge_role_id)

      return [true, political_party_id] if self.get_badge_role.nil? &&
                                           Constants.get_vip_status_badge_role_id.present? && check_quota_limit && self.get_user_invitee_count < COUNT_TO_BE_INVITED
    end
    [false, nil]
  end

  def get_badge_card(political_party_id, show_details = false)
    gradients = get_gradients_for_badge_card(political_party_id)
    role = Role.find(Constants.get_vip_status_badge_role_id)
    badge_data = role.get_role_data_for_badge_card(political_party_id)
    message = 'పార్టీ సర్కిల్ లో V.I.P స్టేటస్ పొందండి'
    total_invited_text = nil
    users_joined_count = 0
    if show_details
      users_joined_count = self.get_user_invitee_count
      message = "*#{COUNT_TO_BE_INVITED}* మంది పార్టీ అభిమానులని యాప్ లో చేర్పించడం ద్వారా *పార్టీ బ్యాడ్జ్* పొందండి."
      total_invited_text = " #{COUNT_TO_BE_INVITED} లో #{users_joined_count} చేరారు"
    end
    {
      feed_type: 'badge_card',
      feed_item_id: 'badge_card',
      message: message,
      total_invited_text: total_invited_text,
      count_to_be_invited: COUNT_TO_BE_INVITED,
      invited_count: users_joined_count,
      circle_id: political_party_id,
      badge: badge_data,
      background_gradients: gradients,
      show_details: show_details,
    }
  end

  def is_eligible_for_user_role
    if self.get_badge_role.nil?
      political_party_id = self.user_exclusive_political_party
      circle_id_has_badge_icon = BadgeIconGroup.where(circle_id: political_party_id).exists? if political_party_id.present?
      if circle_id_has_badge_icon
        political_circle = Circle.find(political_party_id)
        role_id_in_circle = Constants.get_vip_status_badge_role_id
        check_quota_limit = political_circle.check_quota_limit_of_role_in_circle(role_id_in_circle)
        is_invited_by_circle_share = SingularDatum.where(invited_by: self.id,
                                                         utm_campaign: 'Group invite',
                                                         invited_via: political_party_id)
                                                  .where('created_at > ?',
                                                         Time.zone.parse(BADGE_CARD_ENABLED_DATE)).count > 0

        unless check_quota_limit
          Honeybadger.notify(
            "Exceeded the quota limit for role #{role_id_in_circle} in circle #{political_party_id}",
            context: { user_id: id }
          )
        end

        return [true, political_party_id, role_id_in_circle] if political_circle.present? &&
                                                                is_invited_by_circle_share && role_id_in_circle.present? && check_quota_limit &&
                                                                self.get_user_invitee_count >= COUNT_TO_BE_INVITED
      end
    end
    [false, nil, nil]
  end

  def get_gradients_for_badge_card(circle_id)
    tool_tip_color = 0xff606060
    tool_tip_text_color = 0xffFFFFFF
    party_gradients = Circle::PARTY_GRADIENTS[circle_id]

    if party_gradients.present?
      {
        footer_text_color: party_gradients['circle_text_color'],
        badge_text_color: party_gradients['circle_text_color'],
        gradient_direction: {
          begin_x: party_gradients['poster_and_badge_card_bg_gradient_begin_x'],
          begin_y: party_gradients['poster_and_badge_card_bg_gradient_begin_y'],
          end_x: party_gradients['poster_and_badge_card_bg_gradient_end_x'],
          end_y: party_gradients['poster_and_badge_card_bg_gradient_end_y'],
        },
        "footer_gradient_direction": {
          "begin_x": party_gradients['poster_and_badge_card_footer_gradient_begin_x'],
          "begin_y": party_gradients['poster_and_badge_card_footer_gradient_begin_y'],
          "end_x": party_gradients['poster_and_badge_card_footer_gradient_end_x'],
          "end_y": party_gradients['poster_and_badge_card_footer_gradient_end_y']
        },
        upload_tool_tip_color: tool_tip_color,
        upload_tool_tip_text_color: tool_tip_text_color,
        background_gradients: {
          colors: party_gradients['poster_and_badge_card_background_colors'],
          stops: party_gradients['poster_and_badge_card_background_color_stops'],
        },
        "footer_gradients": {
          "colors": party_gradients['poster_and_badge_card_footer_colors'],
          "stops": party_gradients['poster_and_badge_card_footer_color_stops']
        },
        badge_banner_gradients: {
          colors: party_gradients['badge_banner_colors'],
          stops: party_gradients['badge_banner_color_stops'],
        }
      }
    end
  end

  def update_invite_card
    if self.get_badge_role.present? && self.photo.present? && (self.saved_change_to_photo_id? || self.saved_change_to_name?)
      GenerateInviteCard.perform_async(self.id)
    end
  end

  def is_user_village_municipality_or_corporation
    self.village_id.present? && (self.village.municipality_level? || self.village.corporation_level?)
  end

  # L1
  def get_user_ids_of_location_list_1(count)
    following_user_ids = self.following.map(&:user_id)

    grade_levels = self.is_user_village_municipality_or_corporation ? [1, 2, 3] : [1, 2, 3, 4]
    User.joins(user_roles: :role).
      active.
      where('user_roles.purview_circle_id = ? ', self.village_id).
      where("(user_roles.grade_level IN (#{grade_levels.join(",")})) OR
             (user_roles.grade_level IS NULL AND roles.grade_level IN (#{grade_levels.join(",")}))").
      where.not(id: self.id).
      where.not(id: following_user_ids).order(total_followers_count: :desc).limit(count).
      pluck(:id)
  end

  # L1 count
  def get_users_count_of_location_list_1
    following_user_ids = self.following.map(&:user_id)

    grade_levels = self.is_user_village_municipality_or_corporation ? [1, 2, 3] : [1, 2, 3, 4]
    User.joins(user_roles: :role).
      active.
      where('user_roles.purview_circle_id = ? ', self.village_id).
      where("(user_roles.grade_level IN (#{grade_levels.join(",")})) OR
             (user_roles.grade_level IS NULL AND roles.grade_level IN (#{grade_levels.join(",")}))").
      where.not(id: self.id).
      where.not(id: following_user_ids).count
  end

  # L2
  def get_user_ids_of_location_list_2(count)
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('user_roles.purview_circle_id = ? ', self.mandal_id).
      where("(user_roles.grade_level IN (#{[1, 2, 3].join(",")})) OR
             (user_roles.grade_level IS NULL AND roles.grade_level IN (#{[1, 2, 3].join(",")}))").
      where.not(id: self.id).
      where.not(id: following_user_ids).order(total_followers_count: :desc).limit(count).
      pluck(:id)
  end

  # L2 count
  def get_users_count_of_location_list_2
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('user_roles.purview_circle_id = ? ', self.mandal_id).
      where("(user_roles.grade_level IN (#{[1, 2, 3].join(",")})) OR
             (user_roles.grade_level IS NULL AND roles.grade_level IN (#{[1, 2, 3].join(",")}))").
      where.not(id: self.id).
      where.not(id: following_user_ids).count
  end

  # L3
  def get_user_ids_of_location_list_3(count)
    following_user_ids = self.following.map(&:user_id)

    user_location_ids = [self.mla_constituency_id, self.mp_constituency_id].compact

    if user_location_ids.length.positive?
      User.joins(user_roles: :role).
        active.
        where("user_roles.purview_circle_id IN (#{user_location_ids.join(",")})").
        where("(user_roles.grade_level = 2) OR
           (user_roles.grade_level IS NULL AND roles.grade_level = 2 )").
        where.not(id: self.id).
        where.not(id: following_user_ids).order(total_followers_count: :desc).limit(count).
        pluck(:id)
    else
      []
    end
  end

  # L3 count
  def get_users_count_of_location_list_3
    following_user_ids = self.following.map(&:user_id)

    user_location_ids = [self.mla_constituency_id, self.mp_constituency_id].compact

    if user_location_ids.length.positive?
      User.joins(user_roles: :role).
        active.
        where("user_roles.purview_circle_id IN (#{user_location_ids.join(",")})").
        where("(user_roles.grade_level = 2) OR
           (user_roles.grade_level IS NULL AND roles.grade_level = 2 )").
        where.not(id: self.id).
        where.not(id: following_user_ids).count
    else
      0
    end
  end

  # L4
  def get_user_ids_of_location_list_4(count)
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('user_roles.purview_circle_id = ? ', self.district_id).
      where("(user_roles.grade_level = 2) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 2 )").
      where.not(id: self.id).
      where.not(id: following_user_ids).order(total_followers_count: :desc).limit(count).
      pluck(:id)
  end

  # L4 count
  def get_users_count_of_location_list_4
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('user_roles.purview_circle_id = ? ', self.district_id).
      where("(user_roles.grade_level = 2) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 2 )").
      where.not(id: self.id).
      where.not(id: following_user_ids).count
  end

  # L5
  def get_user_ids_of_location_list_5(count)
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('user_roles.purview_circle_id = ? ', self.mandal_id).
      where("(user_roles.grade_level = 4) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 4 )").
      where.not(id: self.id).
      where.not(id: following_user_ids).order(total_followers_count: :desc).limit(count).
      pluck(:id)
  end

  # L5 count
  def get_users_count_of_location_list_5
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('user_roles.purview_circle_id = ? ', self.mandal_id).
      where("(user_roles.grade_level = 4) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 4 )").
      where.not(id: self.id).
      where.not(id: following_user_ids).count
  end

  # L6
  def get_user_ids_of_location_list_6(count)
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('user_roles.purview_circle_id = ? ', self.mla_constituency_id).
      where("(user_roles.grade_level IN (#{[1, 2, 3].join(",")})) OR
             (user_roles.grade_level IS NULL AND roles.grade_level IN (#{[1, 2, 3].join(",")}))").
      where.not(id: self.id).
      where.not(id: following_user_ids).order(total_followers_count: :desc).limit(count).
      pluck(:id)
  end

  # L6 count
  def get_users_count_of_location_list_6
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('user_roles.purview_circle_id = ? ', self.mla_constituency_id).
      where("(user_roles.grade_level IN (#{[1, 2, 3].join(",")})) OR
             (user_roles.grade_level IS NULL AND roles.grade_level IN (#{[1, 2, 3].join(",")}))").
      where.not(id: self.id).
      where.not(id: following_user_ids).count
  end

  # L7
  def get_user_ids_of_location_list_7(count)
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('user_roles.purview_circle_id = ? ', self.mp_constituency_id).
      where('user_roles.parent_circle_id = ? ', self.user_exclusive_political_party).
      where("(user_roles.grade_level = 3) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 3 )").
      where.not(id: self.id).
      where.not(id: following_user_ids).order(total_followers_count: :desc).limit(count).pluck(:id)
  end

  # L7 count
  def get_users_count_of_location_list_7
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('user_roles.purview_circle_id = ? ', self.mp_constituency_id).
      where('user_roles.parent_circle_id = ? ', self.user_exclusive_political_party).
      where("(user_roles.grade_level = 3) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 3 )").
      where.not(id: self.id).
      where.not(id: following_user_ids).count
  end

  # P2
  def get_user_ids_of_interest_list_2(count)
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('(user_roles.parent_circle_id = ? OR (user_roles.parent_circle_id IS NULL AND roles.parent_circle_id = ?))',
            self.user_exclusive_political_party, self.user_exclusive_political_party).
      where('users.mla_constituency_id = ?', self.mla_constituency_id).
      where("(user_roles.grade_level IN (#{[1, 2, 3].join(",")})) OR
             (user_roles.grade_level IS NULL AND roles.grade_level IN (#{[1, 2, 3].join(",")}))").
      where.not(id: self.id).
      where.not(id: following_user_ids).order(total_followers_count: :desc).limit(count).
      pluck(:id)
  end

  # P2 count
  def get_users_count_of_interest_list_2
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('(user_roles.parent_circle_id = ? OR (user_roles.parent_circle_id IS NULL AND roles.parent_circle_id = ?))',
            self.user_exclusive_political_party, self.user_exclusive_political_party).
      where('users.mla_constituency_id = ?', self.mla_constituency_id).
      where("(user_roles.grade_level IN (#{[1, 2, 3].join(",")})) OR
             (user_roles.grade_level IS NULL AND roles.grade_level IN (#{[1, 2, 3].join(",")}))").
      where.not(id: self.id).where.not(id: following_user_ids).count
  end

  # P3
  def get_user_ids_of_interest_list_3(count = MAX_LEN_OF_SUGGESTED_USERS)
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('(user_roles.parent_circle_id = ? OR (user_roles.parent_circle_id IS NULL AND roles.parent_circle_id = ?))',
            self.user_exclusive_political_party, self.user_exclusive_political_party).
      where('users.mandal_id = ?', self.mandal_id).
      where("(user_roles.grade_level IN (#{[4, 5].join(",")})) OR
             (user_roles.grade_level IS NULL AND roles.grade_level IN (#{[4, 5].join(",")}))").
      where.not(id: self.id).
      where.not(id: following_user_ids).order(total_followers_count: :desc).limit(count).
      pluck(:id)
  end

  # P3 count
  def get_users_count_of_interest_list_3
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('(user_roles.parent_circle_id = ? OR (user_roles.parent_circle_id IS NULL AND roles.parent_circle_id = ?))',
            self.user_exclusive_political_party, self.user_exclusive_political_party).
      where('users.mandal_id = ?', self.mandal_id).
      where("(user_roles.grade_level IN (#{[4, 5].join(",")})) OR
             (user_roles.grade_level IS NULL AND roles.grade_level IN (#{[4, 5].join(",")}))").
      where.not(id: self.id).
      where.not(id: following_user_ids).count
  end

  # P4
  def get_user_ids_of_interest_list_4(count = MAX_LEN_OF_SUGGESTED_USERS)
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('(user_roles.parent_circle_id = ? OR (user_roles.parent_circle_id IS NULL AND roles.parent_circle_id = ?))',
            self.user_exclusive_political_party, self.user_exclusive_political_party).
      where('users.mla_constituency_id = ?', self.mla_constituency_id).
      where("(user_roles.grade_level = 3) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 3 )").
      where.not(id: self.id).
      where.not(id: following_user_ids).order(total_followers_count: :desc).limit(count).
      pluck(:id)
  end

  # P4 count
  def get_users_count_of_interest_list_4
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('(user_roles.parent_circle_id = ? OR (user_roles.parent_circle_id IS NULL AND roles.parent_circle_id = ?))',
            self.user_exclusive_political_party, self.user_exclusive_political_party).
      where('users.mla_constituency_id = ?', self.mla_constituency_id).
      where("(user_roles.grade_level = 3) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 3 )").
      where.not(id: self.id).where.not(id: following_user_ids).count
  end

  # P5
  def get_user_ids_of_interest_list_5(count = MAX_LEN_OF_SUGGESTED_USERS)
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('(user_roles.parent_circle_id = ? OR (user_roles.parent_circle_id IS NULL AND roles.parent_circle_id = ?))',
            self.user_exclusive_political_party, self.user_exclusive_political_party).
      where('users.mandal_id = ?', self.mandal_id).
      where("(user_roles.grade_level = 4) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 4 )").
      where.not(id: self.id).
      where.not(id: following_user_ids).order(total_followers_count: :desc).limit(count).
      pluck(:id)
  end

  # P5 count
  def get_users_count_of_interest_list_5
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('(user_roles.parent_circle_id = ? OR (user_roles.parent_circle_id IS NULL AND roles.parent_circle_id = ?))',
            self.user_exclusive_political_party, self.user_exclusive_political_party).
      where('users.mandal_id = ?', self.mandal_id).
      where("(user_roles.grade_level = 4) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 4 )").
      where.not(id: self.id).
      where.not(id: following_user_ids).count
  end

  # P6
  def get_user_ids_of_interest_list_6(count = MAX_LEN_OF_SUGGESTED_USERS)
    following_user_ids = self.following.map(&:user_id)
    User.joins(user_roles: :role).
      active.
      where('(user_roles.parent_circle_id = ? OR (user_roles.parent_circle_id IS NULL AND roles.parent_circle_id = ?))',
            self.user_exclusive_political_party, self.user_exclusive_political_party).
      where('users.mla_constituency_id = ?', self.mla_constituency_id).
      where("(user_roles.grade_level = 3) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 3 )").
      where.not(id: self.id).
      where.not(id: following_user_ids).order(total_followers_count: :desc).limit(count).
      pluck(:id)
  end

  # P6 count
  def get_users_count_of_interest_list_6
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('(user_roles.parent_circle_id = ? OR (user_roles.parent_circle_id IS NULL AND roles.parent_circle_id = ?))',
            self.user_exclusive_political_party, self.user_exclusive_political_party).
      where('users.mla_constituency_id = ?', self.mla_constituency_id).
      where("(user_roles.grade_level = 3) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 3 )").
      where.not(id: self.id).where.not(id: following_user_ids).count
  end

  # P7
  def get_user_ids_of_interest_list_7(count = MAX_LEN_OF_SUGGESTED_USERS)
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('(user_roles.parent_circle_id = ? OR (user_roles.parent_circle_id IS NULL AND roles.parent_circle_id = ?))',
            self.user_exclusive_political_party, self.user_exclusive_political_party).
      where('users.mp_constituency_id = ?', self.mp_constituency_id).
      where("(user_roles.grade_level = 2) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 2 )").
      where.not(id: self.id).
      where.not(id: following_user_ids).order(total_followers_count: :desc).limit(count).
      pluck(:id)
  end

  # P7 count
  def get_users_count_of_interest_list_7
    following_user_ids = self.following.map(&:user_id)

    User.joins(user_roles: :role).
      active.
      where('(user_roles.parent_circle_id = ? OR (user_roles.parent_circle_id IS NULL AND roles.parent_circle_id = ?))',
            self.user_exclusive_political_party, self.user_exclusive_political_party).
      where('users.mp_constituency_id = ?', self.mp_constituency_id).
      where("(user_roles.grade_level = 2) OR
             (user_roles.grade_level IS NULL AND roles.grade_level = 2 )").
      where.not(id: self.id).where.not(id: following_user_ids).count
  end

  def get_suggested_users_feed_v1(loaded_feed_item_ids, is_political_circle = false)
    return nil if $redis.exists?(Constants.disable_suggested_lists_key)

    feed_item_id, user_ids, users_count = self.get_suggested_users_list_v1(loaded_feed_item_ids, is_political_circle)

    return nil if user_ids.empty? || user_ids.count < MIN_LEN_OF_SUGGESTED_USERS

    header = get_suggested_users_list_header(feed_item_id)
    sub_header = AppVersionSupport.is_new_suggested_users_list_enabled? ? 'ఫాలో అవ్వటం ద్వారా మీరు వారి నుండి సమాచారాన్ని ఎప్పటికీ కోల్పోరు' : nil

    suggested_users = User.where(id: user_ids).map do |u|
      {
        id: u.id,
        name: u.name,
        photo: u.photo,
        hashid: u.hashid,
        badge: u.get_badge_role&.get_json,
        avatar_color: u.avatar_color
      }
    end

    feed_type = 'suggested_list'
    suggested_type = 'USERS'
    suggested_sub_type = 'users'
    is_follow_all_present = true
    custom_properties = log_suggested_users_list(feed_item_id, suggested_type, is_follow_all_present)
    {
      header: header,
      sub_header: sub_header,
      feed_item_id: feed_item_id,
      feed_type: feed_type,
      suggested_type: suggested_type,
      suggested_sub_type: suggested_sub_type,
      is_follow_all_present: is_follow_all_present,
      users: suggested_users,
      users_count: users_count,
      custom_properties: custom_properties
    }
  end

  def log_suggested_users_list(suggested_users_list_id, suggested_type, is_follow_all_present)
    {
      suggested_users_list_id: suggested_users_list_id,
      suggested_type: suggested_type,
      is_follow_all_present: is_follow_all_present
    }.compact
  end

  # retruns header based on suggested users list id
  def get_suggested_users_list_header(feed_item_id)
    case true
    when feed_item_id == SUGGESTED_LIST_1
      user_mla_constituency = Circle.find(self.mla_constituency_id)
      user_affiliated_party = Circle.find(self.user_exclusive_political_party)
      "మీ #{user_mla_constituency.name} #{user_affiliated_party.name} నాయకులుని ఫాలో అవ్వండి"
    when feed_item_id == SUGGESTED_LIST_2
      user_district = Circle.find(self.district_id)
      "మీ #{user_district.name} జిల్లా ప్రముఖ నాయకులుని ఫాలో అవ్వండి"
    when feed_item_id == SUGGESTED_LIST_3
      user_mp_constituency = Circle.find(self.mp_constituency_id)
      user_affiliated_party = Circle.find(self.user_exclusive_political_party)
      "మీ #{user_mp_constituency.name} పార్లమెంటులోని #{user_affiliated_party.name} నాయకులుని ఫాలో అవ్వండి"
    when feed_item_id == SUGGESTED_LIST_4
      user_mla_constituency = Circle.find(self.mla_constituency_id)
      "మీ #{user_mla_constituency.name} నియోజకవర్గంలోని స్థానిక నాయకులుని ఫాలో అవ్వండి"
    when feed_item_id == SUGGESTED_LIST_5 || feed_item_id == SUGGESTED_LIST_8
      user_mla_constituency = Circle.find(self.mla_constituency_id)
      user_affiliated_party = Circle.find(self.user_exclusive_political_party)
      "మీ #{user_mla_constituency.name} నియోజకవర్గంలోని #{user_affiliated_party.name} ప్రముఖ నాయకులుని ఫాలో అవ్వండి"
    when feed_item_id == SUGGESTED_LIST_6
      user_mp_constituency = Circle.find(self.mp_constituency_id)
      user_affiliated_party = Circle.find(self.user_exclusive_political_party)
      "మీ #{user_mp_constituency.name} పార్లమెంటులోని #{user_affiliated_party.name} ప్రముఖ రాష్త్ర స్థాయి నాయకులుని ఫాలో అవ్వండి"
    when feed_item_id == SUGGESTED_LIST_7
      user_mp_constituency = Circle.find(self.mp_constituency_id)
      "మీ #{user_mp_constituency.name} పార్లమెంటులోని స్థానిక నాయకులుని ఫాలో అవ్వండి"
    when feed_item_id == SUGGESTED_LIST_9
      user_mp_constituency = Circle.find(self.mp_constituency_id)
      "మీ #{user_mp_constituency.name} పార్లమెంటులోని ప్రముఖ రాష్త్ర స్థాయి నాయకులుని ఫాలో అవ్వండి"
    else
      ''
    end
  end

  # returns list_id, suggested_user_ids and users_count_in_list of any kind of suggested_users_list.
  # If one list is seen more than MAX_SEEN_OF_SUGGESTED_USERS then we shows the next eligible list
  # is_political_circle flag is for showing only party related suggested users in user affiliated party circle.

  def get_suggested_users_list_v1(loaded_feed_item_ids, is_political_circle)
    user_grade = self.get_badge_role&.get_readable_grade_level
    min_length = MIN_LEN_OF_SUGGESTED_USERS

    new_suggested_list = AppVersionSupport.is_new_suggested_users_list_enabled? ? true : false
    max_length = new_suggested_list ? MAX_LEN_OF_SUGGESTED_USERS : MAX_LEN_OF_OLD_SUGGESTED_USERS

    feed_item_id, user_ids_list, users_count = '', [], 0
    if user_grade.nil? || user_grade == 5
      if is_eligible_for_party_suggested_users_list(SUGGESTED_LIST_1, loaded_feed_item_ids)
        feed_item_id, user_ids_list = SUGGESTED_LIST_1, self.get_user_ids_of_interest_list_2(max_length)
        users_count = self.get_users_count_of_interest_list_2 if new_suggested_list
      end

      if user_ids_list.length < min_length && is_eligible_for_location_suggested_users_list(SUGGESTED_LIST_2, loaded_feed_item_ids, is_political_circle)
        feed_item_id, user_ids_list = SUGGESTED_LIST_2, self.get_user_ids_of_location_list_1(max_length) + self.get_user_ids_of_location_list_2(max_length) + self.get_user_ids_of_location_list_3(max_length) + self.get_user_ids_of_location_list_4(max_length)
        users_count = self.get_users_count_of_location_list_1 + self.get_users_count_of_location_list_2 + self.get_users_count_of_location_list_3 + self.get_users_count_of_location_list_4 if new_suggested_list
      end
    elsif user_grade == 4
      if is_eligible_for_party_suggested_users_list(SUGGESTED_LIST_3, loaded_feed_item_ids)
        feed_item_id, user_ids_list = SUGGESTED_LIST_3, self.get_user_ids_of_interest_list_5(max_length) + self.get_user_ids_of_interest_list_6(max_length) + self.get_user_ids_of_interest_list_7(max_length)
        users_count = self.get_users_count_of_interest_list_5 + self.get_users_count_of_interest_list_6 + self.get_users_count_of_interest_list_7 if new_suggested_list
      end

      if user_ids_list.length < min_length && is_eligible_for_location_suggested_users_list(SUGGESTED_LIST_4, loaded_feed_item_ids, is_political_circle)
        feed_item_id, user_ids_list = SUGGESTED_LIST_4, self.get_user_ids_of_location_list_5(max_length) + self.get_user_ids_of_location_list_6(max_length)
        users_count = self.get_users_count_of_location_list_5 + self.get_users_count_of_location_list_6 if new_suggested_list
      end

      if user_ids_list.length < min_length && is_eligible_for_party_suggested_users_list(SUGGESTED_LIST_1, loaded_feed_item_ids)
        feed_item_id, user_ids_list = SUGGESTED_LIST_1, self.get_user_ids_of_interest_list_2(max_length)
        users_count = self.get_users_count_of_interest_list_2 if new_suggested_list
      end

      if user_ids_list.length < min_length && is_eligible_for_location_suggested_users_list(SUGGESTED_LIST_2, loaded_feed_item_ids, is_political_circle)
        feed_item_id, user_ids_list = SUGGESTED_LIST_2, self.get_user_ids_of_location_list_1(max_length) + self.get_user_ids_of_location_list_2(max_length) + self.get_user_ids_of_location_list_3(max_length) + self.get_user_ids_of_location_list_4(max_length)
        users_count = self.get_users_count_of_location_list_1 + self.get_users_count_of_location_list_2 + self.get_users_count_of_location_list_3 + self.get_users_count_of_location_list_4 if new_suggested_list
      end

    elsif user_grade == 3
      if is_eligible_for_party_suggested_users_list(SUGGESTED_LIST_5, loaded_feed_item_ids)
        feed_item_id, user_ids_list = SUGGESTED_LIST_5, self.get_user_ids_of_interest_list_6(max_length)
        users_count = self.get_users_count_of_interest_list_6 if new_suggested_list
      end

      if user_ids_list.length < min_length && is_eligible_for_party_suggested_users_list(SUGGESTED_LIST_6, loaded_feed_item_ids)
        feed_item_id, user_ids_list = SUGGESTED_LIST_6, self.get_user_ids_of_interest_list_7(max_length)
        users_count = self.get_users_count_of_interest_list_7 if new_suggested_list
      end

      if user_ids_list.length < min_length && is_eligible_for_location_suggested_users_list(SUGGESTED_LIST_7, loaded_feed_item_ids, is_political_circle)
        feed_item_id, user_ids_list = SUGGESTED_LIST_7, self.get_user_ids_of_location_list_5(max_length) + self.get_user_ids_of_location_list_7(max_length)
        users_count = self.get_users_count_of_location_list_5 + self.get_users_count_of_location_list_7 if new_suggested_list
      end

      if user_ids_list.length < min_length && is_eligible_for_party_suggested_users_list(SUGGESTED_LIST_8, loaded_feed_item_ids)
        feed_item_id, user_ids_list = SUGGESTED_LIST_8, self.get_user_ids_of_interest_list_4(max_length)
        users_count = self.get_users_count_of_interest_list_4 if new_suggested_list
      end

      if user_ids_list.length < min_length && is_eligible_for_location_suggested_users_list(SUGGESTED_LIST_9, loaded_feed_item_ids, is_political_circle)
        feed_item_id, user_ids_list = SUGGESTED_LIST_9, self.get_user_ids_of_location_list_3(max_length) + self.get_user_ids_of_location_list_4(max_length)
        users_count = self.get_users_count_of_location_list_3 + self.get_users_count_of_location_list_4 if new_suggested_list
      end
    end

    user_ids_list = user_ids_list.slice(0, max_length)
    users_count = users_count - user_ids_list.count

    [feed_item_id, user_ids_list, users_count]
  end

  def get_list_of_suggested_users(list_name)
    interest_suggested_users_list = []
    min_len = MIN_LEN_OF_SUGGESTED_USERS
    max_len = MAX_LEN_OF_SUGGESTED_USERS

    user_ids_list, all_users_count =
      case true
      when list_name == SUGGESTED_LIST_1
        [get_user_ids_of_interest_list_2(max_len), get_users_count_of_interest_list_2]
      when list_name == SUGGESTED_LIST_3
        user_ids_list_5 = get_user_ids_of_interest_list_5(max_len)

        user_ids_list_6 = []
        if user_ids_list_5.count < max_len
          user_ids_list_6 = get_user_ids_of_interest_list_6(max_len)
        end

        user_ids_list_7 = []
        if (user_ids_list_5.count + user_ids_list_6.count) < max_len
          user_ids_list_7 = get_user_ids_of_interest_list_7(max_len)
        end

        count = get_users_count_of_interest_list_5 +
                get_users_count_of_interest_list_6 +
                get_users_count_of_interest_list_7

        user_ids = (user_ids_list_5 + user_ids_list_6 + user_ids_list_7).slice(0, max_len)

        [user_ids, count]
      when list_name == SUGGESTED_LIST_5
        [get_user_ids_of_interest_list_6(max_len), get_users_count_of_interest_list_6]
      when list_name == SUGGESTED_LIST_6
        [get_user_ids_of_interest_list_7(max_len), get_users_count_of_interest_list_7]
      when list_name == SUGGESTED_LIST_8
        [get_user_ids_of_interest_list_4(max_len), get_users_count_of_interest_list_4]
      end

    if user_ids_list.present? && user_ids_list.count >= min_len
      feed_item_id = list_name
      header = get_suggested_users_list_header(list_name)
      sub_header = 'వెంటనే క్లిక్ చేసి ఫాలో అవ్వండి.'

      users_list = User.where(id: user_ids_list).map do |u|
        {
          id: u.id,
          name: u.name,
          photo: u.photo,
          hashid: u.hashid,
          badge: u.get_badge_role&.get_json,
          avatar_color: u.avatar_color
        }
      end

      users_count = all_users_count - users_list.count

      interest_suggested_users_list << [feed_item_id, header, sub_header, users_count, users_list]
    end

    interest_suggested_users_list
  end

  def get_suggested_contacts_feed_object
    return [] unless self.should_show_contact_suggestions?

    header = 'మీ పరిచయస్తులను అనుసరించండి'
    sub_header = 'ఫాలో అవ్వటం ద్వారా మీరు వారి నుండి సమాచారాన్ని ఎప్పటికీ కోల్పోరు'
    feed_item_id = 'CONTACTS_LIST'
    feed_type = 'suggested_list'
    suggested_type = 'USERS'
    suggested_sub_type = 'CONTACTS'
    is_follow_all_present = true
    users_count = 0

    user_contacts_ids = self.not_yet_followed_signed_up_user_ids_of_user_contacts
    return [] if user_contacts_ids.blank?

    if AppVersionSupport.is_new_suggested_users_list_enabled?
      return [] if user_contacts_ids.count < User::MIN_LEN_OF_SUGGESTED_USERS

      users_count = user_contacts_ids.count - User::MAX_LEN_OF_SUGGESTED_USERS if user_contacts_ids.count > User::MAX_LEN_OF_SUGGESTED_USERS

      user_ids = user_contacts_ids.slice(0, User::MAX_LEN_OF_SUGGESTED_USERS)
      users = User.where(id: user_ids).map do |u|
        {
          id: u.id,
          name: u.name,
          photo: u.photo,
          hashid: u.hashid,
          badge: u.get_badge_role&.get_json,
          avatar_color: u.avatar_color
        }
      end
    else
      sub_header = nil
      users = User.where(id: user_contacts_ids).map do |u|
        u.badge = u.get_badge_role&.get_json
        u
      end
    end

    {
      header: header,
      sub_header: sub_header,
      feed_item_id: feed_item_id,
      feed_type: feed_type,
      suggested_type: suggested_type,
      suggested_sub_type: suggested_sub_type,
      is_follow_all_present: is_follow_all_present,
      users: users,
      users_count: users_count
    }
  end

  def update_affiliated_party_circle_id
    self.affiliated_party_circle_id = self.user_exclusive_political_party
    self.save!
  end

  def get_suggested_users_lists
    return [] if $redis.exists?(Constants.disable_suggested_lists_key)

    user_grade = self.get_badge_role&.get_readable_grade_level

    interest_suggested_users_lists = []
    suggested_users_lists = []

    if user_grade.nil? || user_grade == 5
      interest_suggested_users_lists += self.get_list_of_suggested_users(User::SUGGESTED_LIST_1)
    elsif user_grade == 4
      if self.is_eligible_for_party_suggested_users_list(User::SUGGESTED_LIST_3, [])
        interest_suggested_users_lists += self.get_list_of_suggested_users(User::SUGGESTED_LIST_3)
      end

      if self.is_eligible_for_party_suggested_users_list(User::SUGGESTED_LIST_1, [])
        interest_suggested_users_lists += self.get_list_of_suggested_users(User::SUGGESTED_LIST_1)
      end
    elsif user_grade == 3
      if self.is_eligible_for_party_suggested_users_list(User::SUGGESTED_LIST_5, [])
        interest_suggested_users_lists += self.get_list_of_suggested_users(User::SUGGESTED_LIST_5)
      end

      if self.is_eligible_for_party_suggested_users_list(User::SUGGESTED_LIST_6, [])
        interest_suggested_users_lists += self.get_list_of_suggested_users(User::SUGGESTED_LIST_6)
      end

      if self.is_eligible_for_party_suggested_users_list(User::SUGGESTED_LIST_8, [])
        interest_suggested_users_lists += self.get_list_of_suggested_users(User::SUGGESTED_LIST_8)
      end
    end

    interest_suggested_users_lists.map do |list|
      feed_item_id, header, sub_header, users_count, users_list = list[0], list[1], list[2], list[3], list[4]
      suggested_users_lists << {
        header: header,
        sub_header: sub_header,
        feed_item_id: feed_item_id.to_s,
        feed_type: 'suggested_list',
        suggested_type: 'USERS',
        suggested_sub_type: 'users',
        is_follow_all_present: true,
        users: users_list,
        users_count: users_count
      }
    end

    suggested_users_lists
  end

  def is_eligible_for_party_suggested_users_list(list_name, loaded_feed_item_ids)
    $redis.hget("suggested_users_list_#{list_name}", self.id).to_i < MAX_SEEN_OF_SUGGESTED_USERS_LIST && loaded_feed_item_ids.exclude?(list_name)
  end

  def is_eligible_for_location_suggested_users_list(list_name, loaded_feed_item_ids, is_political_circle)
    !is_political_circle && $redis.hget("suggested_users_list_#{list_name}", self.id).to_i < MAX_SEEN_OF_SUGGESTED_USERS_LIST && loaded_feed_item_ids.exclude?(list_name)
  end

  def is_eligible_for_interest_suggested_lists_in_members_tab
    user_grade = self.get_badge_role&.get_readable_grade_level

    if user_grade.nil? || user_grade == 5
      is_eligible_for_party_suggested_users_list(SUGGESTED_LIST_1, [])
    elsif user_grade == 4
      is_eligible_for_party_suggested_users_list(SUGGESTED_LIST_3, []) ||
        is_eligible_for_party_suggested_users_list(SUGGESTED_LIST_1, [])
    elsif user_grade == 3
      is_eligible_for_party_suggested_users_list(SUGGESTED_LIST_5, []) ||
        is_eligible_for_party_suggested_users_list(SUGGESTED_LIST_6, []) ||
        is_eligible_for_party_suggested_users_list(SUGGESTED_LIST_8, [])
    end
  end

  def available_post_comment_options(is_post_create, party_id_on_post = nil)
    party_circle_id = nil
    if (is_post_create && affiliated_party_circle_id.present?) || party_id_on_post.present?
      party_circle_id = party_id_on_post || affiliated_party_circle_id
    end
    exclusive_users_comments_options = []
    if party_circle_id.present?
      party_circle_name = Circle.find(party_circle_id).name
      exclusive_users_comments_options =
        [{
           type: Post.comments_types.key(2),
           display_text: "మీ #{party_circle_name} సభ్యులు మాత్రమే",
           identifier: 2
         },
         {
           type: Post.comments_types.key(3),
           display_text: 'ఇతర పార్టీ సభ్యులు కాకుండా అందరూ',
           identifier: 3
         }]
    end
    [{
       type: Post.comments_types.key(0),
       display_text: 'ప్రతి ఒక్కరూ',
       identifier: 0
     },
     {
       type: Post.comments_types.key(1),
       display_text: 'మరెవరూ కాదు',
       identifier: 1
     }] + exclusive_users_comments_options
  end

  def get_user_permission_group_id_of_circle(circle_id)
    permission_group_id = UserCirclePermissionGroup.get_user_circle_permission_group_id(id, circle_id)
    if permission_group_id.blank?
      circle = Circle.find circle_id
      return get_user_permission_group_id_of_circle(circle.parent_circle_id) if circle.sub_level?

      is_user_joined = self.circles.include? circle
      permission_group_id = CirclePermissionGroup.get_default_circle_permission_group_id(circle_id, circle.circle_type,
                                                                                         circle.level, is_user_joined)
    end
    # Returning the circle id to return back parent circle id, so that it can be further used. Especially for sub circles.
    [circle_id, permission_group_id]
  end

  def block_for_tagging
    $redis.sadd('users_blocked_for_tagging', self.id)
  end

  def block_for_commenting
    $redis.sadd('users_blocked_for_commenting', self.id)
  end

  def unblock_for_tagging
    $redis.srem('users_blocked_for_tagging', self.id)
  end

  def unblock_for_commenting
    $redis.srem('users_blocked_for_commenting', self.id)
  end

  def is_blocked_for_tagging?
    $redis.sismember('users_blocked_for_tagging', self.id)
  end

  def is_blocked_for_commenting?
    $redis.sismember('users_blocked_for_commenting', self.id)
  end

  def create_circle_flow(creating_admin_user_id)
    CreateCircleFlow.perform_async(id, creating_admin_user_id)
  end

  def get_circle_tag_config(circle_id, app_version)
    return nil unless app_version > Gem::Version.new('1.16.3')
    is_user_can_tag_circle = CirclePolicy.new(self, Circle.find(circle_id)).can_add_tag?
    {
      can_tag: is_user_can_tag_circle,
      disabled_text: is_user_can_tag_circle ? '' : 'ఈ సర్కిల్‌ను ట్యాగ్ చేయడానికి మీకు అనుమతి లేదు'
    }
  end

  # new function to get circle tag config for v1 to avoid app version check
  def get_circle_tag_config_v1(circle_id)
    is_user_can_tag_circle = CirclePolicy.new(self, Circle.find(circle_id)).can_add_tag?
    {
      can_tag: is_user_can_tag_circle,
      disabled_text: is_user_can_tag_circle ? '' : 'ఈ సర్కిల్‌ను ట్యాగ్ చేయడానికి మీకు అనుమతి లేదు'
    }
  end

  def get_active_role_constituency_name
    self.user_roles.joins(:role)
        .where(roles: { has_badge: true, purview_level: [:mla_constituency, :mp_constituency] })
        .where('start_date <= ? and end_date >= ?', Date.current, Date.current).first&.purview_circle&.name
  end

  def get_show_contacts_screen_value_from_redis
    # to retrieve the value from redis of the user
    show_follow_contacts_screen = $redis.get(Constants.should_show_contacts_redis_key(self.id))

    case show_follow_contacts_screen
    when 'true'
      true
    when 'false'
      false
    else
      nil
    end
  end

  def set_show_contacts_screen_value_to_redis(value, expiry_time)
    # to set the value to redis of the user
    $redis.set(Constants.should_show_contacts_redis_key(self.id), value, ex: expiry_time)
  end

  # delete the redis value after login to show the contacts screen again if user met the criteria
  def del_redis_keys_of_contact_screen_after_login
    $redis.del(Constants.should_show_contacts_redis_key(self.id))
    $redis.del(Constants.contact_screen_shown_count(self.id))
  end

  def update_login_time
    # create or update last login time of user
    Metadatum.find_or_create_by(key: Constants.last_login_time_key, entity: self).update(value: Time.zone.now)
  end

  def trigger_contact_notification_worker
    if AppVersionSupport.contacts_screen_enabled?
      SendContactNotification.perform_in(5.hours, self.id)
    end
  end

  def get_last_login_time
    self.metadatum.where(key: Constants.last_login_time_key).last&.value&.to_datetime
  end

  def get_last_contacts_uploaded_time
    self.metadatum.where(key: Constants.last_contacts_uploaded_time_key).last&.value&.to_datetime
  end

  def is_user_has_minimum_contacts?
    # get all user_ids of user contacts who are signed up
    user_ids = self.not_yet_followed_signed_up_user_ids_of_user_contacts

    # return if users count less than 5 or badge users count less than 2
    has_min_contacts_for_user = false
    if user_ids.count >= 5
      has_min_contacts_for_user = true
    else
      badge_users_count = 0
      user_ids.each do |user_id|
        user_contact = User.find_by(id: user_id)
        next if user_contact.blank?
        badge_users_count += 1 if user_contact.get_badge_role.present?
        if badge_users_count >= 2
          has_min_contacts_for_user = true
          break
        end
      end
    end
    has_min_contacts_for_user
  end

  def should_show_follow_contacts
    show_follow_contacts_screen = nil

    if AppVersionSupport.contacts_screen_enabled?
      show_follow_contacts_screen = self.get_show_contacts_screen_value_from_redis

      if show_follow_contacts_screen.nil?
        # check last login time of user
        last_login_time = self.get_last_login_time
        if last_login_time.present? && Time.zone.now >= last_login_time + 6.hours
          last_contacts_upload_time = self.get_last_contacts_uploaded_time
          # if the user has uploaded contacts and last login time is less than last contacts upload time
          # and user has minimum contacts then show the contacts screen
          if last_contacts_upload_time.present? && last_login_time <= last_contacts_upload_time && self.is_user_has_minimum_contacts?
            show_follow_contacts_screen = true
          end
        end
      end
    end

    show_follow_contacts_screen
  end

  def contact_screen_max_seen_limit_reached?
    self.get_contact_screen_shown_count_of_user >= Constants.max_contact_screen_seen_limit
  end

  def get_contact_screen_shown_count_of_user
    $redis.get(Constants.contact_screen_shown_count(self.id)).to_i
  end

  def set_contact_screen_shown_count_from_redis
    # to retrieve the value from redis of the user
    count = $redis.get(Constants.contact_screen_shown_count(self.id)).to_i
    if count == 0
      count = 1
    else
      count += 1
    end
    $redis.set(Constants.contact_screen_shown_count(self.id), count, ex: 30.days.to_i)
  end

  def self.get_user_from_jwt_token(token)
    user = nil
    payload = JsonWebToken.decode(token)
    if payload.present? && payload['user_id'].present?
      user = User.active.find_by(id: payload['user_id'])
    end
    user
  end

  def get_blocked_user_ids
    blocked_user_ids = $redis.smembers(Constants.get_user_blocked_ids_key(id)).map(&:to_i)
    if blocked_user_ids.blank?
      blocked_user_ids = BlockedUser.where(user_id: id, active: true).pluck(:blocked_user_id)
      if blocked_user_ids.present?
        $redis.sadd(Constants.get_user_blocked_ids_key(id), blocked_user_ids)
        $redis.expireat(Constants.get_user_blocked_ids_key(id), (Time.zone.now.end_of_day + Constants.blocked_users_cache_expiry_in_days.days).to_i)
      end
    end
    blocked_user_ids
  end

  def get_blocked_by_user_ids
    blocked_by_user_ids = $redis.smembers(Constants.get_user_blocked_by_ids_key(id)).map(&:to_i)
    if blocked_by_user_ids.blank?
      blocked_by_user_ids = BlockedUser.where(blocked_user_id: id, active: true).pluck(:user_id)
      if blocked_by_user_ids.present?
        $redis.sadd(Constants.get_user_blocked_by_ids_key(id), blocked_by_user_ids)
        $redis.expireat(Constants.get_user_blocked_by_ids_key(id), (Time.zone.now.end_of_day + Constants.blocked_users_cache_expiry_in_days.days).to_i)
      end
    end
    blocked_by_user_ids
  end

  def affiliated_circle_ids_for_posters_tab
    # get state_id, affiliated_party_circle_id and affiliated_party_leader_ids and public circle id
    user_affiliated_circle_ids = [self.state_id, 0]
    # For testing only
    user_affiliated_circle_ids << 31407 if self.is_test_user?
    user_affiliated_party_id = self.affiliated_party_circle_id

    if user_affiliated_party_id.present? && user_affiliated_party_id != 0
      # for affiliated user, we need to show the events of party.
      # NOTE:: as per discussion with uday, send only affiliated party joined leaders events and creative kinds only
      user_joined_leader_ids = CirclesRelation.where(first_circle_id: self.get_user_joined_leader_ids(add_limit: true),
                                                     second_circle_id: user_affiliated_party_id,
                                                     active: true,
                                                     relation: 'Leader2Party').pluck(:first_circle_id)

      user_affiliated_circle_ids += user_joined_leader_ids
      user_affiliated_circle_ids << user_affiliated_party_id
    else
      user_joined_party_and_leader_ids = self.get_user_joined_party_and_leader_ids(add_limit: true)
      user_affiliated_circle_ids += user_joined_party_and_leader_ids
    end

    user_affiliated_circle_ids.uniq
  end

  def is_subscription_banner_to_be_enabled_trial_user?
    $redis.sismember(Constants.get_subscription_banner_to_be_enabled_trial_users_key, id)
  end

  def eligible_for_monthly_campaign_for_yearly_users?
    expiry_time = $redis.zscore(Constants.monthly_campaign_for_yearly_users_key, id)
    expiry_time.present? && expiry_time.to_i > Time.zone.now.to_i
  end

  def my_poster_sections_v2(user_poster_layout_id: nil)
    user_affiliated_circle_ids = affiliated_circle_ids_for_posters_tab
    # show subscription banner if user is not paid and user layout data is available and (trial_day is not present
    # or trial_day >= 10)
    subscription_toast, subscription_json = self.get_subscription_data if is_subscription_banner_to_be_enabled_trial_user?
    is_eligible_for_premium_creatives = self.is_eligible_for_premium_creatives?
    events_section = get_events_section_v2(user_affiliated_circle_ids: user_affiliated_circle_ids,
                                           is_eligible_for_premium_creatives: is_eligible_for_premium_creatives)
    creative_kinds_sections = get_creative_kinds_sections_v2(user_affiliated_circle_ids: user_affiliated_circle_ids,
                                                             is_eligible_for_premium_creatives: is_eligible_for_premium_creatives)

    [subscription_toast, subscription_json, events_section,
     creative_kinds_sections].flatten.compact
  end

  def my_poster_sections
    user_affiliated_circle_ids = affiliated_circle_ids_for_posters_tab
    # show subscription banner if user is not paid and user layout data is available and (trial_day is not present
    # or trial_day >= 10)
    subscription_toast, subscription_json = self.get_subscription_data if is_subscription_banner_to_be_enabled_trial_user?

    is_eligible_for_premium_creatives = self.is_eligible_for_premium_creatives?
    events_section = self.get_events_section(user_affiliated_circle_ids, is_eligible_for_premium_creatives)
    creative_kinds_sections = self.get_creative_kinds_sections(user_affiliated_circle_ids, is_eligible_for_premium_creatives)

    [subscription_toast, subscription_json, events_section, creative_kinds_sections].flatten.compact
  end

  #@deprecated
  def get_subscription_data
    return [nil, nil]
    order = Order.where(user: self).open.last
    return [nil, nil] if order.blank? || !has_premium_layout?

    subscription_toast = self.get_subscription_toast(order)
    subscription_json = self.get_subscription_json(order)

    [subscription_toast, subscription_json]
  end

  #@deprecated
  def get_subscription_toast(order)

    if order.pending?
      message = 'మీ మునుపటి ట్రాన్సాక్షన్ ప్రాసెస్ చేయబడుతోంది. దయచేసి కాసేపు ఆగి చూడండి.'
      icon_color = 0xff664d03
      close_icon_color = 0xff664d03
      toast_color = 0xfffff2cd
      header_font_color = 0xff664d03
      message_font_color = 0xff664d03
    elsif order.last_transaction_failed?
      message = 'మీ మునుపటి ట్రాన్సాక్షన్ విఫలమైనది. దయచేసి మళ్లీ ప్రయత్నించండి.'
      icon_color = 0xff180000
      close_icon_color = 0xff180000
      toast_color = 0xfffeb9b9
      header_font_color = 0xff180000
      message_font_color = 0xff180000
    else
      return
    end

    {
      "feed_type": 'feed_toast',
      "feed_item_id": nil,
      "header": nil,
      "message": message,
      "icon_color": icon_color,
      "close_icon_color": close_icon_color,
      "icon_size": 18.0,
      "icon_code_point": 0xf8bf,
      "toast_color": toast_color,
      "header_font_color": header_font_color,
      "message_font_color": message_font_color,
      "header_font_size": 14.0,
      "message_font_size": 12.0,
      "is_removable": true,
      "is_clickable": true,
      "redirect_to": nil
    }
  end

  #@deprecated
  def get_subscription_json(order)
    subscribe_info_text = 'ప్రీమియం పోస్టర్లు కోసం సబ్స్క్రైబ్ చెయ్యండి'
    if order.pending?
      subscribe_info_text = 'ట్రాన్సాక్షన్ ప్రాసెస్ చేయబడుతోంది..'
    end

    subscribe_banner_text = 'జాయిన్!'
    if order.pending?
      subscribe_banner_text = 'కాసేపు ఆగి చెక్ చేయండి..'
    end

    {
      feed_type: 'subscription_banner',
      image_url: 'https://a-cdn.thecircleapp.in/fit-in/256x256/assets/posters-payment-placeholder.png',
      subscribe_info_text: subscribe_info_text,
      subscribe_banner_text: subscribe_banner_text,
      subscription_screen: get_subscription_screen_json(order)
    }
  end

  #@deprecated
  def get_subscription_screen_json(order)
    return nil
    return nil if order.blank? || order.pending?

    {
      image_url: 'https://a-cdn.thecircleapp.in/assets/posters-payment-placeholder.png',
      header: 'ప్రీమియం పోస్టర్లు',
      sub_header: nil,
      subscription_header: nil,
      subscription_sub_header: 'ప్రీమియం సబ్‌స్క్రిప్షన్ ద్వారా, మీ కోసం ప్రత్యేకంగా రూపొందించిన టాప్ పోస్టర్లను పొందగలరు',
      subscribe_button: {
        period: "#{order.order_items.first&.duration_in_months} " + (order.order_items.first&.duration_in_months == 1 ? 'నెల' : 'నెలలు'),
        content: 'ప్రత్యేక పోస్టర్లు',
        # TODO:: order.order_products.map { |op| op.product.name }.join(" + ")
        # get this from order_items for multiple order items . as of now ignore it
        amount: order.payable_amount,
      },
      disclaimer: 'ప్రీమియం ధరపై ప్రారంభ తగ్గింపు',
      terms: terms_json,
      free_button_text: 'ఉచిత పోస్టర్‌లతో కొనసాగించండి',
      suggested_text: nil,
      share_button_text: 'చెల్లింపు లింక్ షేర్ చేయండి',
      order_id: "#{order.id}"
    }
  end

  def get_events_section_v2(user_affiliated_circle_ids:, is_eligible_for_premium_creatives:)
    sub_query_for_free_users = get_sub_query_for_free_users(is_eligible_for_premium_creatives)

    Event.fetch_events_data_as_json_v2(user_affiliated_circle_ids: user_affiliated_circle_ids,
                                       sub_query_for_free_users: sub_query_for_free_users)
  end

  def get_creative_kinds_sections_v2(user_affiliated_circle_ids:, is_eligible_for_premium_creatives:)
    sub_query_for_free_users = get_sub_query_for_free_users(is_eligible_for_premium_creatives)

    PosterCreative.fetch_category_kind_data_as_json_v2(user_affiliated_circle_ids: user_affiliated_circle_ids,
                                                       sub_query_for_free_users: sub_query_for_free_users)

  end

  def get_events_section(user_affiliated_circle_ids, is_eligible_for_premium_creatives)
    sub_query_for_free_users = get_sub_query_for_free_users(is_eligible_for_premium_creatives)

    events = Event.events_for_posters_tab(user_affiliated_circle_ids: user_affiliated_circle_ids, sub_query_for_free_users: sub_query_for_free_users)

    if events.blank?
      return
    end
    events_section = {
      feed_type: 'poster_section',
      title: 'ఈవెంట్స్'
    }

    events_section[:categories] = events.map do |event|
      poster_creatives_counts, poster_creative_urls_of_event = event.get_poster_creative_urls(sub_query_for_free_users)
      {
        title: event.name,
        params: {
          id: event.id.to_s
        },
        image_urls: poster_creative_urls_of_event,
        total_creatives_count: poster_creatives_counts,
        analytics_params: event.analytics_params
      }
    end
    events_section
  end

  def get_creative_kinds_sections(user_affiliated_circle_ids, is_eligible_for_premium_creatives)
    sub_query_for_free_users = get_sub_query_for_free_users(is_eligible_for_premium_creatives)
    app_version_check_where_clause = "AND poster_creatives.photo_v3_id IS NOT NULL"
    creative_kinds_sections = []

    PosterCreative.get_creative_kinds_for_sections.each do |creative_kind|
      circle_ids_with_poster_creatives = PosterCreativeCircle.joins(:poster_creative)
                                                             .where(poster_creatives: { creative_kind: creative_kind,
                                                                                        active: true })
                                                             .where(" ? > poster_creatives.start_time AND ? <
                                                              poster_creatives.end_time #{sub_query_for_free_users}
                                                                    #{app_version_check_where_clause}
                                                                    ", Time.zone.now, Time.zone.now)
                                                             .where(circle_id: user_affiliated_circle_ids)
                                                             .order(primary: :desc, id: :desc)
                                                             .group_by(&:circle_id)
                                                             .transform_values { |poster_creative_circles|
                                                               poster_creative_circles.map(&:poster_creative) }

      creative_kind_section = {
        feed_type: 'poster_section',
        title: PosterCreative.get_creative_kind_verbose(creative_kind) }

      categories = []
      circle_ids_with_poster_creatives.each do |circle_id, creatives|
        circle_name = Circle.find(circle_id).name
        image_urls = creatives.map(&:photo_v3).map(&:url).first(3)
        categories << { title: circle_name,
                        params: {
                          category_kind: creative_kind,
                          circle_id: circle_id.to_s
                        },
                        image_urls: image_urls,
                        analytics_params: {
                          category_kind: creative_kind,
                          circle_id: circle_id,
                          circle_name: circle_name
                        }.compact
        }
      end
      creative_kind_section[:categories] = categories

      next if creative_kind_section[:categories].blank?
      creative_kinds_sections << creative_kind_section
    end

    creative_kinds_sections
  end

  def get_sub_query_for_free_users(has_premium_layout)
    unless has_premium_layout
      sub_query_for_free_users = 'AND poster_creatives.paid = false'
    else
      sub_query_for_free_users = ''
    end
    sub_query_for_free_users
  end

  def get_poster_creatives(category_id = nil, category_kind = nil, circle_id = nil, creative_id = nil,
                           from_admin_dashboard: false)
    # as per discussion with product team, we cannot show premium posters to free users

    is_eligible_for_premium_creatives = self.is_eligible_for_premium_creatives?

    if category_id.present?
      # get poster_creatives id, photo_v2, photo_v1
      # order by primary desc and id desc
      # primary desc means primary poster_creative should come first

      poster_creatives = PosterCreative.of_event(event_id: category_id, include_paid: is_eligible_for_premium_creatives,
                                                 creative_id: creative_id,
                                                 include_expired: false, include_inactive: false)
    elsif category_kind.present? && circle_id.present?
      poster_creatives = PosterCreative.of_kind_in_circle(kind: category_kind, circle_id: circle_id,
                                                          include_paid: is_eligible_for_premium_creatives,
                                                          creative_id: creative_id, include_expired: false,
                                                          include_inactive: false)
    elsif creative_id.present?
      poster_creatives = PosterCreative.get_creatives(creative_id: creative_id,
                                                      include_paid: true,
                                                      include_expired: false, include_inactive: false)
    end
    creatives = []
    # to check if the user is eligible for premium layout
    # is_locked variable is to lock the premium creative which has basic layout for non premium users
    is_layout_locked = !self.is_poster_subscribed
    return [] if poster_creatives.blank?
    poster_creatives.each do |poster_creative|
      creatives << poster_creative.build_creative_json(user: self, posters_tab_v3_enabled: true,
                                                       is_layout_locked: is_layout_locked,
                                                       is_eligible_for_premium_creatives: is_eligible_for_premium_creatives,
                                                       category_kind: category_kind, circle_id: circle_id,
                                                       category_id: category_id, creative_id: creative_id,
                                                       from_admin_dashboard:)

    end
    creatives
  end

  # paid creatives will be shown to the user if user is subscribed
  def is_eligible_for_premium_creatives?
    self.is_poster_subscribed
  end

  def has_premium_layout?
    @has_premium_layout ||= get_user_poster_layout.present?
  end

  def get_user_poster_layout
    @user_poster_layout ||= UserPosterLayout.where(entity: self, active: true).last
  end

  def get_user_poster_layout_including_inactive
    @user_poster_layout_including_inactive ||= UserPosterLayout.where(entity: self).last
  end

  def get_is_user_position_back_for_posters_tab_v2
    name_length = Unicode::DisplayWidth.of(self.name)
    case true
      # for short name
    when name_length <= 10
      false
      # for long name
    when name_length > 10
      true
    end
  end

  def get_identity_type_based_on_name_size(is_neutral_frame, identity_type, user_role)
    # Calculate lengths
    name_length = Unicode::DisplayWidth.of(self.name)
    badge_description_length = user_role.blank? ? 0 : Unicode::DisplayWidth.of(user_role.get_description)

    # Define criteria for identity type
    is_neutral_flat_identity = is_neutral_frame && identity_type.to_sym == :flat_user
    is_flat_or_gold_user = identity_type.to_sym.in?([:flat_user, :gold_lettered_user])
    name_and_badge_fit = name_length <= 10 && badge_description_length <= 14

    # Determine the identity type based on criteria
    if is_neutral_flat_identity || (is_flat_or_gold_user && name_and_badge_fit)
      "#{identity_type}_front"
    elsif is_flat_or_gold_user && !name_and_badge_fit
      "#{identity_type}_back"
    else
      identity_type
    end
  end

  def get_user_opted_frame_ids_with_is_locked(is_subscribed)
    frame_ids_with_is_locked = {}
    frame_ids = is_subscribed ? SubscriptionUtils.get_user_subscribed_frame_ids(self.id) : UserFrame.get_user_frame_ids(self.id)
    frame_ids.each { |frame_id| frame_ids_with_is_locked[frame_id] = !is_subscribed }
    frame_ids_with_is_locked
  end

  def eligible_for_29_rs_campaign?
    expire_time = $redis.zscore(Constants.premium_29_rs_campaign_redis_key, id)
    expire_time.present? && expire_time > Time.zone.now.to_i
  end

  def eligible_for_1_rs_campaign?
    expire_time = $redis.zscore(Constants.premium_1_rs_user_redis_key, id)
    expire_time.present? && expire_time > Time.zone.now.to_i
  end

  def eligible_for_59_rs_campaign?
    expire_time = $redis.zscore(Constants.premium_59_rs_campaign_redis_key, id)
    expire_time.present? && expire_time > Time.zone.now.to_i
  end

  def eligible_for_one_month_special_campaign?
    eligible_for_1_rs_campaign? || eligible_for_29_rs_campaign? || eligible_for_59_rs_campaign?
  end

  def eligible_for_half_price_discount?
    expire_time = $redis.zscore(Constants.premium_half_price_campaign_redis_key, id)
    expire_time.present? && expire_time > Time.zone.now.to_i
  end

  def is_eligible_for_start_trial?
    return false if SubscriptionUtils.has_user_ever_subscribed?(self.id)

    true
  end

  def eligible_for_self_trial?
    return false unless AppVersionSupport.self_trial_supported?
    return false if SubscriptionUtils.has_user_ever_subscribed?(self.id)
    return false if has_premium_layout?

    true
  end

  def is_trial_user?
    # Return false if the user is not eligible for premium layout
    return false unless has_premium_layout?

    # Return false if the user is subscribed
    return false if is_poster_subscribed

    @is_trial_user ||= begin
                         trial_start_date, trial_duration = Metadatum.get_user_trial_start_date_and_duration(self)
                         trial_start_date.present? && trial_duration.positive? && Time.zone.today <= trial_start_date.advance(days: trial_duration - 1)
                       end
  end

  def trial_expired?
    @trial_expired ||= begin
                         trial_start_date, trial_duration = Metadatum.get_user_trial_start_date_and_duration(self)
                         trial_start_date.present? && trial_duration.positive? && Time.zone.today > trial_start_date.advance(days: trial_duration - 1)
                       end
  end

  def is_trial_extension_user?
    @trial_extension_user ||= Metadatum.where(entity_type: 'User', entity_id: self.id, key: Constants.user_poster_trial_start_date_key).count > 1
  end

  # check if user has ever been in trial
  def has_ever_been_in_trial?
    trial_start_date, trial_duration = Metadatum.get_user_trial_start_date_and_duration(self)
    trial_start_date.present? && trial_duration.positive?
  end

  def get_gradient_circle(affiliated_circle_id, creative_circle_id, category_id, has_creative_id: false)
    return Circle.find_by(id: 31405) if creative_circle_id == 37980
    return Circle.find_by(id: 31402) if creative_circle_id == 37981
    return Circle.find_by(id: 31398) if creative_circle_id == 37982
    return Circle.find_by(id: 31403) if creative_circle_id == 37983
    gradient_circle_id = nil
    if has_creative_id && !category_id.present?
      gradient_circle_id = creative_circle_id
      creative_circle = Circle.find_by(id: creative_circle_id)
      if creative_circle.present? && (creative_circle.location_circle_type? || creative_circle_id == 0)
        gradient_circle_id = affiliated_circle_id
      elsif creative_circle.present? && creative_circle.political_leader_level?
        political_circle = creative_circle.get_leader_circle_party
        return political_circle if political_circle.present?
      elsif creative_circle.present? && creative_circle.political_party_level?
        gradient_circle_id = creative_circle_id
      end
    elsif affiliated_circle_id.present? && affiliated_circle_id != 0
      if creative_circle_id.present?
        creative_circle = Circle.find_by(id: creative_circle_id)
        if creative_circle.present? && (creative_circle.location_circle_type? || creative_circle_id == 0)
          gradient_circle_id = affiliated_circle_id
        elsif creative_circle.present? && creative_circle.political_leader_level?
          gradient_circle_id = affiliated_circle_id if CirclesRelation.where(first_circle_id: creative_circle_id,
                                                                             second_circle_id: affiliated_circle_id,
                                                                             relation: 'Leader2Party',
                                                                             active: true).exists?
        elsif creative_circle.present? && creative_circle.political_party_level?
          gradient_circle_id = creative_circle_id == affiliated_circle_id ? creative_circle_id : 0
        else
          gradient_circle_id = affiliated_circle_id
        end
      elsif category_id.present?
        category_circle_ids = EventCircle.where(event_id: category_id).pluck(:circle_id)
        gradient_circle_id = affiliated_circle_id if (category_circle_ids & [affiliated_circle_id, 0, state_id]).present?
      end
    end
    Circle.find_by(id: gradient_circle_id.to_i)
  end

  def non_badge_user_eligible_for_premium_pitch?
    $redis.sismember(Constants.non_badge_users_for_premium_pitch_key, id)
  end

  def probable_premium_user?
    $redis.sismember(Constants.probable_premium_user_key, id)
  end

  def leader_profession?
    user_profession = UserProfession.find_by(user_id: id)
    return false if user_profession.blank?

    user_profession.leader?
  end

  def create_floww_contact
    response = FlowwApi.create_or_update_contact(id)

    if response['contact_id'].present?
      attributes = {
        entity_type: 'User',
        entity_id: self.id,
        key: Constants.floww_contact_id_key,
        value: response['contact_id']
      }
      Metadatum.find_by(entity: self, key: Constants.floww_contact_id_key)&.update!(value: attributes[:value]) ||
        Metadatum.create!(attributes)
    end
  end

  def create_zoho_contact
    response = ZohoApi.create_contact(self)

    if response['id'].present?
      attributes = {
        entity_type: 'User',
        entity_id: self.id,
        key: Constants.zoho_contact_id_key,
        value: response['id']
      }
      Metadatum.find_by(entity: self, key: Constants.zoho_contact_id_key)&.update!(value: attributes[:value]) ||
        Metadatum.create!(attributes)

      response['id']
    end
  end

  def send_as_fresh_lead_to_crm(lead_type_suffix)
    raise StandardError, "Already a lead in Floww CRM" if get_floww_contact_id.present?

    pp = self.premium_pitch
    lead_type_prefix = get_badge_role.present? ? :LT_ : :BLT_
    lead_type = "#{lead_type_prefix}#{lead_type_suffix}".to_sym
    if pp.present?
      if pp.lead_type.nil?
        pp.lead_type = lead_type
      end
      pp.update(status: :to_be_pitched)
    else
      pp = PremiumPitch.create!(user: self, lead_type: lead_type, source: :REFERRAL_TO_RM)
    end

    pp.shown_interest!
  end

  def find_nearest_paid_user
    User.joins(:user_plan)
        .where(mandal_id: self.mandal_id, status: :active)
        .where.not(id: self.id)
        .select("users.*, MAX(user_plans.updated_at) AS latest_subscription")
        .group("users.id")
        .having("latest_subscription IS NOT NULL")
  end

  def get_badge
    self.roles.first&.name || 'No Badge'
  end

  def verify_eligibility_rules_for_premium_pitch?(check_self_trial_eligibility: false)
    return false unless AppVersionSupport.supports_autopay?
    if check_self_trial_eligibility
      return false unless AppVersionSupport.self_trial_supported?
      return false if SubscriptionUtils.has_user_ever_subscribed?(id)
    end
    # return false if PremiumPitch.todays_leads_count >= Constants.daily_premium_leads_limit
    return false if self.has_premium_layout?

    true
  end

  def is_eligible_for_premium_pitch?
    return false unless verify_eligibility_rules_for_premium_pitch?

    # start_time = Time.zone.today + 11.hours # 11 AM of today
    # end_time = Time.zone.today + 18.hours + 30.minutes # 6:30 PM of today
    #
    # return false unless Time.zone.now.between?(start_time, end_time)

    (self.is_test_user? ||
      self.free_poster_share_count >= Constants.poster_usage_count_threshold_for_premium_pitch ||
      get_badge_role_including_unverified.present? ||
      leader_profession? ||
      non_badge_user_eligible_for_premium_pitch? ||
      probable_premium_user?) &&
      (premium_pitch.nil? || premium_pitch.to_be_pitched?)
  end

  def is_eligible_for_premium_pitch_v2?
    return false unless verify_eligibility_rules_for_premium_pitch?(check_self_trial_eligibility: true)

    true
  end

  def get_slogan(identity_type = nil, user_role = nil, circle)
    if identity_type&.to_sym == :party_slogan_identity
      user_badge = user_role&.get_json
      party_slogan = user_badge&.dig('description')
    else
      party_slogan = circle&.slogan
    end
    party_slogan
  end

  def get_slogan_icon_url(identity_type = nil, circle)
    if identity_type&.to_sym == :plain_identity_with_party_icon
      slogan_icon_url = circle&.slogan_icon&.url
    else
      slogan_icon_url = nil
    end
    slogan_icon_url
  end

  def get_user_layouts(category_id: nil, circle_id: nil, category_kind: nil, user_poster_layout_id: nil,
                       is_from_edit_layouts: false, creative_id: nil)
    user_layouts = []
    user_role = self.get_badge_role_including_unverified
    has_poster_layout_data = self.has_premium_layout?
    affiliated_circle = fetch_poster_affiliated_circle(circle_id: circle_id, category_id: category_id,
                                                       has_creative_id: creative_id.present?,
                                                       has_poster_layout_data: has_poster_layout_data)
    frame_circle = Circle.find_by(id: circle_id) if circle_id.present?

    circle_poster_layout_protocol = nil

    # get circle frames if circle has layout and here we are sending basic frames with the circle layout protocol &
    # is_praja_logo false
    has_circle_package = frame_circle.circle_package.present? if frame_circle.present?
    has_active_layout = Circle.has_active_layout?(frame_circle.id) if frame_circle.present?
    circle_frames = fetch_circle_frames(frame_circle: frame_circle, category_kind: category_kind,
                                        has_active_layout: has_active_layout, has_circle_package: has_circle_package)
    circle_frame_ids = circle_frames.map(&:id)

    # get circle layout variation and common hash for layout of circle for circle_frames.
    circle_common_hash_for_layout = {}
    if circle_frames.present?
      circle_layout_variation, circle_common_hash_for_layout = self.get_common_hash_for_layout_and_layout_variation(user_poster_layout_id, entity: frame_circle)
      circle_poster_layout_protocol = frame_circle.get_circle_poster_layout
      share_text = get_circle_framed_poster_share_text(frame_circle)
      circle_common_hash_for_layout.merge!({ share_text: }) if share_text.present?
    end
    is_poster_subscribed = self.is_poster_subscribed

    if user_poster_layout_id.present? || has_poster_layout_data
      context = {
        category_id: category_id,
        creative_id: creative_id,
        is_from_edit_layouts: is_from_edit_layouts,
        user_poster_layout_id: user_poster_layout_id,
        user_role: user_role,
        user_poster_layout_protocol: get_user_poster_layout,
        circle_frames: circle_frames,
        circle_frame_ids: circle_frame_ids,
        affiliated_circle: affiliated_circle,
        circle_layout_variation: circle_layout_variation,
        circle_poster_layout_protocol: circle_poster_layout_protocol
      }

      build_layouts_for_poster_layout_present_users(user_layouts, context)
    else
      user_layouts = get_user_layouts_for_non_layout_user(circle_id: circle_id, category_id: category_id,
                                                          creative_id: creative_id, user_role: user_role,
                                                          circle_frames: circle_frames,
                                                          circle_common_hash_for_layout: circle_common_hash_for_layout)
    end

    categorized_layouts = categorize_layouts(user_layouts, circle_frame_ids)

    basic_layouts = categorized_layouts[:basic]
    premium_neutral_layouts = categorized_layouts[:premium_neutral]
    premium_layouts = categorized_layouts[:premium]
    circle_layouts = categorized_layouts[:circle]

    add_sponsorship_to_circle_layouts(circle_layouts, frame_circle)

    order_premium_layouts!(premium_layouts)

    # order the user layouts
    ordered_layouts = order_layouts({
                                      premium_layouts: premium_layouts,
                                      circle_layouts: circle_layouts,
                                      premium_neutral_layouts: premium_neutral_layouts,
                                      basic_layouts: basic_layouts,
                                      circle_frames: circle_frames,
                                      creative_id: creative_id,
                                      is_poster_subscribed: is_poster_subscribed,
                                      has_poster_layout_data: has_poster_layout_data,
                                      is_eligible_for_basic_frames_at_last: -> { is_eligible_for_basic_frames_at_last? }
                                    })

    add_premium_or_fan_poster_request_layout(ordered_layouts, { circle_id: circle_id,
                                                                category_kind: category_kind,
                                                                user_role: user_role,
                                                                frame_circle: frame_circle,
                                                                has_active_layout: has_active_layout,
                                                                has_circle_package: has_circle_package,
                                                                user_poster_layout_id: user_poster_layout_id,
                                                                circle_frames: circle_frames })

    add_analytics_params_to_layouts(ordered_layouts, circle_id, user_role)
    ordered_layouts.compact
  end

  #@note: This method is used to get single layout for Poster Campaign Purpose
  # This will return pitch premium layout if there is no layout for user
  def get_user_layout(frame_id: nil)
    # stacked frames:- the frames that contains stacked identity plate.
    # So, the plate height and width is out of bounds that we can't able to capture. So we are excluding these frames
    stacked_frame_identity_types = %w[glassy_user]
    user_layouts = get_user_layouts # Fetch user layouts

    if frame_id.present?
      layout = user_layouts.find { |user_layout| user_layout[:id]&.to_i == frame_id.to_i }
    elsif has_premium_layout?
      layout = user_layouts.find { |user_layout| user_layout[:layout_type] == 'status' }
      # If no 'status' layout is found, fall back to 'premium'
      # and exclude stacked frames from the list
      layout ||= user_layouts.find do |user_layout|
        user_layout[:layout_type] == 'premium' &&
          !stacked_frame_identity_types.include?(user_layout[:identity]&.dig(:type)) # identity is a hash
      end
    else
      # We will be showing premium pitch for the users who doesn't have any layout (While Poster Generation for Campaign)
      user_profile_photo_url = self.photo&.url
      user_role = self.get_badge_role_including_unverified
      premium_pitch = get_pitch_premium_layout(user_role, use_flat_user_badge_circle_identity: true,
                                               user_photo_url: user_profile_photo_url)
      layout = premium_pitch
    end

    layout
  end

  def build_basic_frames_json(circle:, user_role: nil, user_poster_layout_id: nil)
    basic_layouts = []
    basic_frames = Frame.basic_frames
    basic_frames.each do |basic_frame|
      layout_type = basic_frame.frame_type
      gold_border = basic_frame.gold_border
      has_shadow_color = basic_frame.has_shadow_color
      identity_type = basic_frame.identity_type
      layout = self.get_layout(layout_type, gold_border, has_shadow_color, circle, user_role,
                               nil, nil, identity_type, user_poster_layout_id,
                               original_layout_type: layout_type)
      layout[:id] = basic_frame.id
      layout[:identity] = layout[:identity].merge(type: identity_type, show_badge_ribbon: basic_frame.badge_strip)
      layout[:is_locked] = false
      layout[:header_2_photos] = circle.leader_photos_for_premium_posters
      layout[:is_bordered_layout] = false
      layout[:enable_outer_frame] = (circle.circle_type&.to_sym == :interest)
      # add a param to check whether layout is a basic_layout or not
      layout[:is_basic_layout] = true
      layout[:frame_type] = basic_frame.frame_type
      basic_layouts << layout
    end
    basic_layouts
  end

  def get_fan_poster_layout(frame_circle:, user_poster_layout_id: nil, user_role: nil)
    fan_poster_request_frames = []
    fan_poster_request_frames << Frame.basic_frames.first
    circle_layout_variation, circle_common_hash_for_layout = self.
      get_common_hash_for_layout_and_layout_variation(user_poster_layout_id, entity: frame_circle,
                                                      has_fan_poster_request: true)

    gradient_circle = frame_circle
    if frame_circle.political_leader_level?
      # add leader affiliation circle only so that party gradients will come up
      party_circle = frame_circle.get_leader_circle_party
      gradient_circle = party_circle if party_circle.present?
    end

    get_circle_layouts_for_non_layout_user(circle: frame_circle,
                                           frames: fan_poster_request_frames,
                                           common_hash: circle_common_hash_for_layout,
                                           user_role: user_role, gradient_circle: gradient_circle).first
  end

  def get_fan_poster_view_count(circle_id)
    $redis.get(Constants.fan_poster_view_count_key(self.id, circle_id)).to_i
  end

  def update_fan_poster_view_count(circle_id)
    key = Constants.fan_poster_view_count_key(self.id, circle_id)
    views_count = $redis.get(key).to_i
    views_count > 0 ? $redis.incr(key) : $redis.set(key, 1, ex: 3.months.to_i)
    views_count
  end

  def get_layout_feedback_prompt
    {
      text: I18n.t('layout_feedback_prompt.title'),
      accept_text: I18n.t('layout_feedback_prompt.accept_text'),
      reject_text: I18n.t('layout_feedback_prompt.reject_text')
    }
  end

  def get_layout_feedback_prompt_v2
    {
      text: I18n.t('layout_feedback_prompt_v2.title'),
      accept_text: I18n.t('layout_feedback_prompt_v2.accept_text'),
      reject_text: I18n.t('layout_feedback_prompt_v2.reject_text')
    }
  end

  # this is the method we need to use for my-poster-page
  # using user_photo_url only to show the user photo in flat user badge circle identity for Poster Campaign purpose
  # Don't send user_photo_url for other cases as it won't work right now
  def get_pitch_premium_layout(user_role, use_flat_user_badge_circle_identity: false, user_photo_url: nil)
    # check if user position is back
    # is_user_position_back = false
    # name_type = self.name_type
    # badge_text_type = self.badge_text_type unless name_type == "long"
    # is_user_position_back = true if name_type == "long" || badge_text_type == "long"
    is_user_position_back = true # always true for trapezoidal_identity
    affiliated_circle = Circle.find_by(id: affiliated_party_circle_id.to_i)

    satisfies_campaign_pitch_criteria = use_flat_user_badge_circle_identity && user_photo_url.present?
    # define the layout, frame and identity
    layout = 'layout_2_3'
    layout_details = UserPosterLayout::LAYOUTS[layout.to_sym]
    identity_type = satisfies_campaign_pitch_criteria ? 'flat_user_badge_circle' : 'trapezoidal_identity'
    layout_type = 'premium'

    # populate the header_1 and header_2 dummy cutout images
    header_1_photos = []
    header_2_photos = []
    leader_photos = []
    h1_photos = []
    h2_photos = []
    h1_photos_count = 2
    h2_photos_count = 3
    if affiliated_party_circle_id.present?
      h1_photos = CirclePhoto.where(circle_id: affiliated_party_circle_id, photo_type: :poster)
                             .order(:photo_order)
                             .map(&:photo).first(2)
      h1_photos_count -= h1_photos.length
    end
    dummy_cutout_photo = AdminMedium.find_by(id: Constants.get_leader_dummy_cutout_id)
    h1_photos += [dummy_cutout_photo] * h1_photos_count if h1_photos_count > 0 && dummy_cutout_photo.present?
    h2_photos = [dummy_cutout_photo] * h2_photos_count if dummy_cutout_photo.present?

    leader_photos += h1_photos
    leader_photos += h2_photos

    leader_photos.each_with_index do |photo, index|
      if index < 2
        header_1_photos << {
          radius: layout_details[:photos][index][:radius],
          position_x: layout_details[:photos][index][:position_x],
          position_y: layout_details[:photos][index][:position_y],
          photo_url: photo.compressed_url(size: 200)
        }
      else
        header_2_photos << {
          radius: layout_details[:photos][index][:radius],
          position_x: layout_details[:photos][index][:position_x],
          position_y: layout_details[:photos][index][:position_y],
          photo_url: photo.compressed_url(size: 200)
        }
      end
    end

    fonts_config = default_font_config(is_user_position_back)
    gradients = affiliated_circle.get_gradients_for_posters_tab(self, false, identity_type)
    user_dummy_photo = AdminMedium.find_by(id: Constants.user_dummy_cutout_id)
    user = user_json_for_layout(user_role, layout_type, original_layout_type: layout_type)

    # add a dummy badge if user doesn't have a badge
    user[:badge] = {
      "id": 1,
      "active": true,
      "badge_text": 'మీ హోదా',
      "description": 'మీ హోదా',
      "badge_banner": 'SILVER'
    } if user[:badge].blank?

    if satisfies_campaign_pitch_criteria
      user[:photo_url] = user_photo_url
    else
      user[:photo_url] = user_dummy_photo.compressed_url(size: 200) if user_dummy_photo.present?
    end

    # For premium pitch layout, we consider it as a dummy photo since it's a preview
    # This key will help to not show the camera icon in the user photo to avoid photo change
    is_dummy_photo = true

    identity = {
      user: user,
      type: identity_type,
      is_user_position_back: is_user_position_back,
      show_badge_ribbon: false,
      party_highlight_color_primary: affiliated_circle.get_primary_highlight_color(identity_type),
      party_highlight_color_secondary: affiliated_circle.get_secondary_highlight_color(identity_type),
      party_icon_url: get_affiliated_party_icon(affiliated_party_circle_id),
      is_dummy_photo: is_dummy_photo
    }

    premium_pitch_json = {
      "title": 'ప్రీమియం పోస్టర్స్ పొందండి',
      "description": 'మీ ప్రియతమ నాయకుల ఫొటోలతో అంతులేని ప్రీమియం డిజైన్లు. *₹299/నెల* నుండి ప్రారంభం',
      "cta_text": is_no_trial_experiment_user? ? 'ఇప్పుడే ప్రీమియం పొందండి' : '15 రోజులు ఉచితంగా పొందండి',
      "submit_url": '/save-premium-lead'
    }
    if eligible_for_self_trial?
      premium_pitch_json[:type] = 'deeplink'
      premium_pitch_json[:deeplink] = get_paywall_deeplink(source: "premium_pitch_posters_feed")
    end

    {
      "id": 0,
      "layout_type": layout_type,
      "golden_frame": false,
      "show_praja_logo": false,
      "shadow_color": nil,
      "v1": nil,
      "share_text": ' ',
      "gradients": gradients,
      "text_color": 0xff000000,
      "badge_text_color": 0xff000000,
      "premium_pitch": premium_pitch_json,
      "identity": identity,
      "header_1_photos": header_1_photos,
      "header_2_photos": header_2_photos,
      "is_locked": true,
      "is_bordered_layout": false,
      "neutral_frame": false,
      "enable_outer_frame": false,
      "frame_type": layout_type,
      "fonts_config": fonts_config,
      "show_camera_icon": false,
      "analytics_params": {
        "layout_type": layout_type,
        "is_locked": true,
        "golden_frame": false,
        "header_1_count": 2,
        "header_2_count": 4,
        "identity_type": identity_type,
        "is_user_position_back": is_user_position_back,
        "is_dummy_photo": is_dummy_photo,
        "party_icon_position": nil,
        "frame_type": layout_type,
        "is_circle_sponsored_frame": false,
        "is_premium_pitch": true,
        "user_posters_subscription_status": get_subscription_status,
        "premium_pitch_view_count": get_premium_pitch_view_count + 1,
        "is_no_trial_experiment_user": is_no_trial_experiment_user? ? "yes" : "no"
      }
    }
  end

  def is_no_trial_experiment_user?
    # id.odd?
    false
  end

  def get_premium_pitch_view_count
    key = Constants.premium_pitch_views_key + '_' + id.to_s
    $redis.get(key).to_i
  end

  def update_pitch_premium_layout_view_count
    key = Constants.premium_pitch_views_key + '_' + id.to_s
    views_count = $redis.get(key).to_i
    views_count > 0 ? $redis.incr(key) : $redis.set(key, 1, ex: 3.months.to_i)
    views_count
  end

  def default_font_config(user_position_back)
    if user_position_back
      {
        "name": {
          "font_family": 'Anek Telugu',
          "font_style": 'normal',
          "font_weight": 'w700'
        },
        "badge": {
          "font_family": 'Noto Sans Telugu',
          "font_style": 'normal',
          "font_weight": 'w700'
        }
      }
    else
      {
        "name": {
          "font_family": 'Noto Sans Telugu',
          "font_style": 'normal',
          "font_weight": 'w700'
        },
        "badge": {
          "font_family": 'Noto Sans Telugu',
          "font_style": 'normal',
          "font_weight": 'w700'
        }
      }
    end
  end

  def get_common_hash_for_layout_and_layout_variation(user_poster_layout_id = nil, entity:, has_fan_poster_request: false)
    layout, header_1_photos, header_2_photos = get_layout_and_header_photos(user_poster_layout_id, entity: entity)
    order = Order.where(user: self).open.last
    if entity.is_a?(Circle) && has_fan_poster_request
      fan_poster_request = Circle.get_fan_poster_request_json(entity.id)
    end
    [layout, {
      header_1_photos: header_1_photos,
      header_2_photos: header_2_photos,
      subscription_screen: self.get_subscription_screen_json(order),
      fan_poster_request: fan_poster_request
    }.compact]
  end

  def get_share_text_for_poster_v1
    tip_text = 'మీ ప్రొఫైల్ ఫోటోను జోడించండి'
    {
      photo_upload_tool_tip: tip_text,
      share_text: self.get_poster_share_text
    }
  end

  def get_layout(layout_type, gold_border, has_shadow_color, circle, user_role,
                 layout_variation, party_icon_url, identity_type = nil, user_poster_layout_id = nil,
                 original_layout_type:)
    # for family frame we should not send badge, protocol and his party gradients
    # we need to send neutral gradients for family frame
    if original_layout_type == 'family_frame_premium'
      circle = Circle.find_by(id: 0)
    end
    poster_v1 = nil
    if layout_type == 'basic' && (user_poster_layout_id.present? || !AppVersionSupport.basic_poster_to_premium_json)
      self.badge = self.get_badge_role&.get_json
      poster_v1 = {
        id: id,
        user: self,
        photo_url: '',
        frame_upload_info: '',
        frame_upload_button_text: 'ఫోటో జోడించండి',
        poster_variant: 'NORMAL',
        leaders_photo_urls: circle.get_circle_photos,
        leader_photo_ring_color: nil,
        share_card_text: self.get_share_text_for_poster_v1,
        gradients: circle.get_gradients_v2(self, true)
      }

    end
    shadow_colors = circle.get_neon_frame_colors if has_shadow_color
    {
      layout_type: layout_type,
      golden_frame: gold_border.blank? ? false : gold_border,
      show_praja_logo: true,
      shadow_color: shadow_colors.blank? ? nil : shadow_colors,
      v1: poster_v1,
      share_text: layout_type == 'basic' ? get_poster_share_text : get_premium_poster_share_text,
      gradients: circle.get_gradients_for_posters_tab(self, gold_border, identity_type),
      text_color: get_text_color(circle, gold_border, identity_type),
      badge_text_color: layout_type == 'basic' && AppVersionSupport.show_badge_banner_support_in_basic_frames? ?
                          get_overrided_badge_text_color_for_basic_frame(circle, identity_type) :
                          circle.get_badge_text_color(identity_type),
      identity: { user: self.user_json_for_layout(user_role, layout_type, identity_type:, original_layout_type:) },
      party_icon: self.get_party_icon_hash(layout_variation, circle, party_icon_url, gold_border)
    }
  end

  def get_overrided_badge_text_color_for_basic_frame(circle, identity_type)
    if identity_type&.to_sym == :flat_user && circle.circle_type&.to_sym != :interest
      0xff000000
    else
      circle.get_badge_text_color(identity_type)
    end
  end

  def get_text_color(circle, gold_border, identity_type)
    case true
    when identity_type&.to_sym.in?([:polygonal_profile_identity, :premium_cornered_party_icon_shiny_identity,
                                    :party_tag_identity])
      circle.get_text_color
    when identity_type&.to_sym.in?([:multi_color_identity, :party_slogan_identity_with_party_icon,
                                    :linear_name_and_role_identity, :trapezoidal_identity, :top_trapezoidal_identity,
                                    :bottom_trapezoidal_identity, :glassy_user, :party_slogan_identity,
                                    :plain_identity_with_party_icon])
      0xff000000
    when identity_type&.to_sym == :premium_cornered_party_icon_gradient_identity && gold_border
      0xff000000
    when identity_type&.to_sym == :gold_lettered_user
      nil
    when identity_type&.to_sym == :flat_user && circle.circle_type&.to_sym != :interest
      0xff000000
    when gold_border
      0xff144995
    else
      circle.get_text_color
    end
  end

  def get_layout_and_header_photos(user_poster_layout_id = nil, entity:)
    if user_poster_layout_id.present?
      user_poster_layout = UserPosterLayout.find_by(id: user_poster_layout_id)
    else
      user_poster_layout = UserPosterLayout.where(entity: entity, active: true).last
    end
    user_leader_photos = UserLeaderPhoto.where(user_poster_layout_id: user_poster_layout.id).order(:header_type, :priority)

    header_1_photos = []
    header_2_photos = []
    layout = "layout_#{user_poster_layout.h1_count}_#{user_poster_layout.h2_count}"
    layout_details = UserPosterLayout::LAYOUTS[layout.to_sym]
    photos_count = 0
    user_leader_photos.each do |user_leader_photo|
      photo_details = layout_details[:photos][photos_count]
      photo_hash = {
        radius: photo_details[:radius],
        position_x: photo_details[:position_x],
        position_y: photo_details[:position_y],
      }

      # In posters v3, we enabled the frames which are in ratio of 4:5,so we are multiplying it by 4/5 to handle the position_y and radius
      photo_hash[:radius] = (photo_hash[:radius] * 4.0) / 5.0
      photo_hash[:position_y] = (photo_hash[:position_y] * 4.0) / 5.0

      if user_leader_photo.header_type == 'header_1'
        photo_hash[:photo_url] = user_leader_photo.photo.compressed_url(size: 200)
        header_1_photos << photo_hash
      elsif user_leader_photo.header_type == 'header_2'
        photo_hash[:photo_url] = user_leader_photo.photo.compressed_url(size: 200)
        header_2_photos << photo_hash
      end
      photos_count += 1
    end
    [layout, header_1_photos, header_2_photos]
  end

  def get_party_icon_hash(layout, circle, party_icon_url, gold_border)
    return nil if circle.id == 0 || party_icon_url.blank?

    position = UserPosterLayout::LAYOUTS[layout.to_sym][:party_icon_position]
    {
      url: party_icon_url,
      position: position,
      gradients: circle.get_gradients_for_party_icon(position, gold_border)
    }
  end

  def get_affiliated_party_icon(circle_id)
    badge_icon_url = BadgeIconGroup.includes(:badge_icons => [:admin_medium]).where("badge_icons.color = 'WHITE'
      AND badge_icon_groups.circle_id = ?", circle_id).pluck(:url).first

    return nil if badge_icon_url.blank?

    badge_icon_url.gsub(Photo::PHOTO_REGEX, 'https://a-cdn.thecircleapp.in/')
  end

  def user_json_for_layout(user_role, layout_type, identity_type: nil, original_layout_type:)
    # don't send badge for family frame
    badge = (original_layout_type == 'family_frame_premium' || identity_type == 'party_slogan_identity') ? nil : user_role&.get_json

    # if badge is present and custom_role_name is present then send custom_role_name in badge description
    # and set badge_banner to NONE
    if badge.present? && !custom_role_name.blank?
      badge["description"] = custom_role_name
      badge["badge_banner"] = "NONE"
    end

    {
      id: self.id,
      name: send_user_name_for_poster(original_layout_type:),
      photo_url: build_poster_photo_url(layout_type:, identity_type:, original_layout_type:),
      badge: badge
    }
  end

  def send_user_name_for_poster(original_layout_type:)
    if original_layout_type == 'family_frame_premium'
      self&.family_frame_name || self.name
    else
      self.name
    end
  end

  # we have different poster photo of user so we are sending based on the layout type or identity type or identifier
  def build_poster_photo_url(layout_type:, identity_type: nil, original_layout_type:)
    if layout_type == 'basic'
      ''
    elsif identity_type == 'flat_user_badge_circle'
      self.poster_photo_with_background&.url
    elsif original_layout_type == 'family_frame_premium'
      self.family_frame_photo&.url || Constants.family_frame_dummy_cutout_url
    elsif original_layout_type == 'hero_frame_premium'
      self.hero_frame_photo&.url || Constants.hero_frame_dummy_cutout_url
    else
      self.poster_photo&.url
    end
  end

  def get_poster_share_text
    deeplink_uri = URI.parse("praja://buzz.praja.app/posters")

    link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/5oqr')
    link_uri.query = URI.encode_www_form({ _dl: deeplink_uri.to_s, _ddl: deeplink_uri.to_s, paffid: self.id })

    link = link_uri.to_s
    link = Singular.shorten_link(link)

    'మీరు కూడా ఈ విధంగా *మీ ఫోటో మరియు మీ పేరుతో* ఉన్న  పోస్టర్ ని ఇప్పుడు *Praja App* లో ఉచితంగా పొందవచ్చు.' + "\n" +
      'తెలుగు రాష్ట్రాలలో *లక్షలాది* మంది రోజు వాడుతున్న సరికొత్త *పొలిటికల్ సోషల్ మీడియా* యాప్. ఇప్పుడే *డౌన్లోడ్* చేసుకోని నాయకుల తో *కనెక్ట్* అవ్వచ్చు అలాగే మీ *స్థానిక విషయాలు* తెలుసుకోవచ్చు.👇👇' + "\n" +
      "#{link}"
  end

  def get_premium_poster_share_text

    share_text = "\nనా అఫీషియల్ అప్డేట్స్ కోసం నన్ను *Praja App* లో ఫాలో అవ్వండి.👇👇"

    deeplink_uri = URI.parse("praja://buzz.praja.app/users/#{self.id}")

    link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/p411')
    link_uri.query = URI.encode_www_form({ _dl: deeplink_uri.to_s, _ddl: deeplink_uri.to_s, paffid: self.id })

    link = link_uri.to_s
    link = Singular.shorten_link(link)

    share_text += "\n#{link}"

    share_text
  end

  def get_circle_framed_poster_share_text(circle)
    return nil if circle.blank?

    is_official = circle.official?
    if circle.political_leader_level? && is_official
      share_text = "#{circle.name} గారు మీ కోసం *స్పెషల్ పోస్టర్‌* స్పాన్సర్ చేసారు."
    elsif circle.political_party_level? && is_official
      share_text = "#{circle.name} మీ కోసం *స్పెషల్ పోస్టర్‌* స్పాన్సర్ చేసింది."
    else
      share_text = "*#{circle.name}* సర్కిల్ లో మీకోసం *స్పెషల్ పోస్టర్*."
    end

    share_text += "\n\nవెంటనే పోస్టర్ పొందండి."

    deeplink_uri = URI.parse("praja://buzz.praja.app/circles/#{circle.id}")

    link_uri = URI.parse('https://prajaapp.sng.link/A3x5b/5oqr')
    link_uri.query = URI.encode_www_form({ _dl: deeplink_uri.to_s, _ddl: deeplink_uri.to_s, paffid: self.id })

    link = link_uri.to_s
    link = Singular.shorten_link(link)

    share_text += "\n#{link}"

    share_text
  end

  def get_h2_background_gradients_of_user(creative_circle_id: nil, category_id: nil, has_creative_id: false,
                                          has_layout_data:)
    if !has_layout_data && creative_circle_id.present?
      affiliated_circle = Circle.find_by(id: creative_circle_id)
      if affiliated_circle.present? && affiliated_circle.political_leader_level?
        political_circle = affiliated_circle.get_leader_circle_party
        affiliated_circle = political_circle if political_circle.present?
      end
    else
      affiliated_circle_id = self.affiliated_party_circle_id.to_i
      affiliated_circle = get_gradient_circle(affiliated_circle_id, creative_circle_id, category_id,
                                              has_creative_id: has_creative_id)
    end
    affiliated_circle.get_gradients_for_h2_background
  end

  # TODO: keeping it for the sake of the internal released versions
  def get_user_dm_circles(circle_ids)
    circles_hash = {}
    dm_circles = circles.dm_groups
                        .or(circles.where(level: Constants.channel_enabled_circle_levels, conversation_type: :channel))
                        .where(id: circle_ids)

    dm_circles.each do |circle|
      senders = circle.users.map { |user| user.get_user_response_hash_for_dm(self) }

      circles_hash[circle.id] = {
        id: circle.id,
        name: circle.name,
        name_en: circle.name_en,
        level: circle.level,
        circle_type: circle.circle_type,
        hashid: circle.hashid,
        photo: circle.photo,
        description: circle.short_info,
        senders: senders
      }
    end

    circles_hash
  end

  # circles batch fetch
  def get_circles(circle_ids)
    circles_hash = {}

    combined_circles = Circle.includes(:parent_circle)
                             .from('circles FORCE INDEX (index_circles_on_id_and_level_and_circle_type)')
                             .where(id: circle_ids)
                             .where("level IN (?) OR (circle_type = ? AND level = ?)",
                                    Constants.channel_enabled_circle_levels.map { |l| Circle::CIRCLE_LEVEL.index(l) },
                                    Circle::CIRCLE_TYPE.index(:user_created),
                                    Circle::CIRCLE_LEVEL.index(:private_group))
                             .all

    joined_circle_ids = self.circles.where(id: circle_ids).pluck(:id)

    combined_circles.each do |circle|
      next if circle.blank? ||
              circles_hash[circle.id].present? ||
              (circle.user_created_circle_type? && circle.private_group_level? && !joined_circle_ids.include?(circle.id))

      circle_hash = circle.get_short_json
      is_circle_member = joined_circle_ids.include?(circle.id)
      circle_hash[:analytics_params][:is_user_circle_member] = is_circle_member.present? ? true : false
      circle_hash[:members_ids] = circle.private_group_level? && is_circle_member.present? ? UserCircle.where(circle_id: circle.id).pluck(:user_id) : []
      circle_hash[:parent_circle] = circle.parent_circle&.get_short_json if circle.sub_level?
      circles_hash[circle.id] = circle_hash
    end

    circles_hash
  end

  # sending user dm circles info to messaging service including permissions
  def get_user_dm_circles_info
    excluded_circle_ids_with_conversation_types = ExcludedUserCircle.where(user_id: id).pluck(:circle_id, :conversation_type)
    excluded_channel_circle_ids = excluded_circle_ids_with_conversation_types.select { |_, conversation_type| conversation_type == 'channel' }.map(&:first)
    excluded_private_group_circle_ids = excluded_circle_ids_with_conversation_types.select { |_, conversation_type| conversation_type == 'private_group' }.map(&:first)

    circle_ids_and_joined_at_array = circles.dm_groups.where.not(id: excluded_private_group_circle_ids)
                                            .or(circles.where(level: Constants.channel_enabled_circle_levels,
                                                              conversation_type: :channel).where.not(id: excluded_channel_circle_ids))
                                            .pluck('circles.id, circles.level, circles.circle_type, circles.parent_circle_id, user_circles.created_at, circles.conversation_type')
    circles_info = {}
    circles_info[:info] =
      circle_ids_and_joined_at_array.map do |circle_id, circle_level, circle_type, parent_circle_id, joined_at, conversation_type|
        joined_at = Circle.get_user_joined_at_for_dm(parent_circle_id, id) if Circle::CIRCLE_LEVEL[circle_level.to_i] == :sub
        permissions = get_all_circle_permissions(circle_id)
        {
          circle_id: circle_id,
          permissions: permissions,
          joined_at: joined_at,
          supported_conversation_types: [conversation_type],
          is_platform_created: circle_type.to_sym != :user_created
        }
      end

    circles_info
  end

  # send user permissions for a circle
  def get_user_dm_circle_info(circle_id)
    circle = Circle.find_by_id(circle_id)
    excluded_user_circle = ExcludedUserCircle.where(user_id: id, circle_id: circle_id, conversation_type: circle.conversation_type).first
    joined_at = if excluded_user_circle.present?
                  nil
                elsif circle.sub_level?
                  Circle.get_user_joined_at_for_dm(circle.parent_circle_id, id)
                else
                  Circle.get_user_joined_at_for_dm(circle.id, id)
                end
    permissions = excluded_user_circle.present? ? [] : get_all_circle_permissions(circle_id)
    {
      circle_id: circle_id,
      permissions: permissions,
      joined_at: joined_at,
      supported_conversation_types: [circle.conversation_type],
      is_platform_created: circle.circle_type.to_sym != :user_created
    }
  end

  def mla_contestants_of_user_mla_constituency
    # currently this is building for andhra mla constituency contestants only
    # YSRCP, TDP, JSP , BJP,INC & other parties
    # Order of contestants - YSRCP, TDP, JSP, BJP, INC & other parties (descending order of members_count)
    tg_party_ids_order = [31403, 31402, 31406, 37788, 37967]
    # Find the index of user_affiliated_party_id in the array
    if affiliated_party_circle_id.present?
      index_of_affiliated_party_id = tg_party_ids_order.index(affiliated_party_circle_id)
      # Move the element to the front of the array if it exists
      if index_of_affiliated_party_id
        tg_party_ids_order.unshift(tg_party_ids_order.delete_at(index_of_affiliated_party_id))
      else
        tg_party_ids_order.unshift(affiliated_party_circle_id)
      end
    end

    score = 0

    # get all the circles of user mla constituency with score
    mla_contestant_circle_relations = CirclesRelation.joins('INNER JOIN circles_relations cr2 ON circles_relations.second_circle_id = cr2.first_circle_id')
                                                     .select(:second_circle_id,
                                                             Arel.sql("(CASE
        WHEN cr2.second_circle_id = #{tg_party_ids_order.first} THEN #{score}
        WHEN cr2.second_circle_id IN (#{tg_party_ids_order[1..3].join(',')}) THEN #{score += 1}
        WHEN cr2.second_circle_id = #{tg_party_ids_order.last} THEN #{score += 1}
        WHEN cr2.second_circle_id NOT IN (#{tg_party_ids_order.first(5).join(',')}) THEN #{score += 1}
        END) AS score"))
                                                     .where(first_circle_id: mla_constituency_id, active: true)
                                                     .where("cr2.relation = 'Leader2Party' AND cr2.active = 1")
                                                     .order(:score)

    # tg_parties need not to be sorted based on members count
    sorted_circle_ids = []
    # other than tg_parties need to be sorted based on members count
    unsorted_circle_ids = []
    # divide the circles into sorted and unsorted circles based on score
    mla_contestant_circle_relations.each do |mla_contestant_circle_relation|
      if mla_contestant_circle_relation.score < score
        sorted_circle_ids << mla_contestant_circle_relation.second_circle_id
      else
        unsorted_circle_ids << mla_contestant_circle_relation.second_circle_id
      end
    end

    unsorted_circles = Circle.where(id: unsorted_circle_ids)
    # sort the unsorted circles based on members count descending order
    sorted_circles_by_members_count = unsorted_circles.sort_by { |circle| -circle.members_count }
    # sort by is used to sort the circles based on the order of ids in the array
    sorted_circles = Circle.where(id: sorted_circle_ids).sort_by { |circle| sorted_circle_ids.index(circle.id) }
    # show the sorted circles first and then unsorted circles
    circles = sorted_circles + sorted_circles_by_members_count

    suggested_mla_contestant_circles = []
    circles.each do |circle|
      leader_circle_party = circle.get_leader_circle_party
      circle.is_user_joined = circle.check_user_joined(self)
      circle.photo&.compressed_url!(size: 200)
      circle.circle_banner = leader_circle_party&.get_circle_banner
      circle.short_name = leader_circle_party&.short_name
      suggested_mla_contestant_circles << circle
    end
    suggested_mla_contestant_circles
  end

  def check_email_eligibility_for_otp
    user_circle_permission_groups = self.user_circle_permission_groups
    if user_circle_permission_groups.present?
      user_circle_permission_groups.each do |user_circle_permission_group|
        if PermissionGroup::WEB_APP_ENABLED_GROUPS.include? user_circle_permission_group.permission_group.name.to_sym
          return true
        end
      end
    end
    false
  end

  def get_short_json
    {
      id: id,
      name: name,
      short_bio: short_bio,
      photo: photo,
    }
  end

  def generate_web_app_jwt_token
    nano_id = Nanoid.generate
    token = JsonWebToken.encode({ user_id: id, user_hash_id: hashid, nano_id: nano_id, created_at: Time.zone.now.to_i })
    [token, nano_id]
  end

  def name_type
    if name.length > 10
      'long'
    else
      'short'
    end
  end

  def badge_text_type
    badge_role = self.get_badge_role
    badge_role.present? ? badge_role.badge_description_type : nil
  end

  def max_photos_count
    is_test_user? ? 10 : 3
  end

  def update_poster_affiliated_party_id(party_id)
    # create or update the poster affiliated party id
    metadata = Metadatum.find_by(key: Constants.poster_affiliated_party_key, entity: self)
    if party_id.present?
      metadata ||= Metadatum.new(key: Constants.poster_affiliated_party_key, entity: self)
      metadata.update(value: party_id) if party_id.to_s != metadata.value
    else
      metadata&.destroy
    end
  end

  def get_poster_affiliated_party_id
    Metadatum.find_by(key: Constants.poster_affiliated_party_key, entity: self)&.value
  end

  def is_eligible_to_send_post_message?
    count = $redis.smembers(Constants.user_post_id_via_dm_key(id)).count
    count < 1 ? true : false
  end

  def update_post_ids_sent_via_dm(post_id)
    key = Constants.user_post_id_via_dm_key(id)
    key_exists = $redis.exists?(key)

    # add the post ID to the set
    $redis.sadd(key, post_id.to_s)

    unless key_exists
      # set expiry if key doesn't exists
      $redis.expireat(key, Time.zone.now.end_of_day.to_i)
    end
  end

  def get_constituencies_feed
    constituencies_feed = []
    circles = Circle.joins('INNER JOIN circles_relations ON circles_relations.second_circle_id = circles.id')
                    .where(circles_relations: { first_circle_id: mandal_id, relation: 'Mandal2MLA' })
                    .pluck('circles.id', 'circles.name, circles.parent_circle_id')

    if circles.present?
      if circles.length == 1
        # if there is only one constituency, then set the mla_constituency_id and mp_constituency_id directly
        self.mla_constituency_id = circles.first[0]
        self.mp_constituency_id = circles.first[2]
        self.save
      else
        options = circles.map { |id, name| { "id": id.to_s, "value": name } }

        # Adding timestamp to feed item id to ignore state of the previous feed item
        constituencies_feed << {
          "feed_type": 'selection_form',
          "feed_item_id": "#{Constants.constituency_feed_item_id_prefix}#{Time.zone.now.to_i}",
          "title": 'మీ నియోజికవర్గాన్ని ఎంచుకోండి',
          "subtitle": 'మీ నియోజకవర్గ ఎన్నికల సమాచారం మరియు ఫలితాలు తెలుసుకోగలరు',
          "image_url": '',
          "image_width": 800,
          "image_height": 400,
          "cta_text": 'కన్ఫర్మ్',
          "options": options,
          "allow_multiple_selection": false,
          "submit_url": '/mla-constituency/submit',
          "post_submit_action": 'refresh',
        }
      end
    end
    constituencies_feed
  end

  # user is eligible for premium pitch if user has a badge and user is not subscribed to posters and
  # view count is less than 2
  def premium_pitch_hot_lead?
    is_eligible_for_premium_pitch? && get_premium_pitch_view_count < 2
  end

  def viewed_kyc_carousel_ids_with_views
    # convert values to integer because redis returns values as strings
    $redis.hgetall(Constants.kyc_carousel_views_key(id)).transform_values(&:to_i)
  end

  def add_carousel_viewed_count(carousel_id)
    # Key for storing views count
    key = Constants.kyc_carousel_views_key(id)

    # Check if the key already exists and set expiry if it doesn't
    unless $redis.exists(key)
      # Assuming 'id' is available in this context; otherwise, it needs to be passed to the method or determined within it
      $redis.hset(key, carousel_id, 0) # Initialize the hash key-value pair if it doesn't exist
      $redis.expire(key, 3.months.to_i) # Set expiry for 3 months
    end

    # Increment the view count
    $redis.hincrby(key, carousel_id, 1)
  end

  def find_or_initialize_premium_pitch(source: nil, lead_type_suffix: 'Inbound')
    premium_pitch = self.premium_pitch
    if premium_pitch.blank?
      lead_type = self.premium_pitch_lead_type(source: lead_type_suffix)
      premium_pitch = PremiumPitch.new(user_id: self.id, lead_type: lead_type, source: source)
    end

    premium_pitch
  end

  def premium_pitch_lead_type(source: 'Inbound')
    get_badge_role = self.get_badge_role
    if self.is_no_trial_experiment_user?
      lead_type = get_badge_role.present? ? "L_#{source}" : "BL_#{source}"
    else
      lead_type = get_badge_role.present? ? "LT_#{source}" : "BLT_#{source}"
    end
    lead_type
  end

  def send_charge_message(subscription_charge)
    sender_id = 'PRAJAA'
    requested_url = 'https://api.textlocal.in/send/?'
    uri = URI.parse(requested_url)

    message = "ప్రజా ప్రీమియం వినియోగదారులుగా ఉన్నందుకు ధన్యవాదాలు. మీ ప్రజా ప్రీమియం, ₹#{subscription_charge.amount} తో #{subscription_charge.charge_date.strftime('%d/%m/%Y')} న ఆటోమెటికగా రీఛార్జ్ చేయబడుతుంది.\n- Praja App"

    Net::HTTP.post_form(
      uri,
      'apikey' => Rails.application.credentials[:text_local_api_key],
      'numbers' => "91#{self.phone}",
      'message' => message,
      'sender' => sender_id,
      'unicode' => true,
      'test' => Rails.env.development?
    )
  end

  def release_build_number
    if self.app_version.present?
      app_version_splits = self.app_version.split('.')
      return 0 if app_version_splits.size < 2
      (app_version_splits[0].to_s + app_version_splits[1].to_s).to_i
    else
      0
    end
  end

  def get_juspay_customer_id
    juspay_id = UserMetadatum.find_by(user_id: id, key: Constants.juspay_customer_id_key)&.value
    juspay_id = JuspayPaymentUtils.create_customer(self) if juspay_id.blank?

    juspay_id
  end

  def get_juspay_client_auth_token
    juspay_customer = JuspayPaymentUtils.get_customer(self)
    juspay_customer.dig('juspay', 'client_auth_token')
  end

  def layout_approval_enabled?
    premium_pitch&.is_layout_approval_flow_enabled? || has_premium_layout?
  end

  private

  def check_signed_up_user
    if active_status? && previous_changes.key?(:status)
      ProcessSignedUpContact.perform_async(id)
      # set_signed_up_today redis key
      self.set_signed_up_today
      # make every user follow Praja official account
      FollowPrajaUser.perform_async(id)
    end
  end

  def self.get_build_number_from_app_version(app_version)
    if User::OLDER_APP_BUILD_NUMBERS.has_key?(:"#{app_version}")
      User::OLDER_APP_BUILD_NUMBERS[:"#{app_version}"]
    else
      10
    end
  end

  def frame_recommendations
    # if name is changed or affiliated_party_circle_id is changed, then we need to update frame recommendations for the
    # user who has user poster layout data and not subscribed to posters yet
    if (previous_changes.key?(:name) || previous_changes.key?(:affiliated_party_circle_id)) &&
       UserPosterLayout.where(entity: self).exists? && !is_poster_subscribed
      # first delete the existing frame recommendations
      self.delete_frame_recommendations
      # create new frame recommendations
      self.create_frame_recommendations
    end
  end

  def self.core_party_user_ids
    $redis.smembers('core_party_user_ids').map(&:to_i)
  end

  def change_tag
    if (saved_change_to_poster_photo_id? or
      saved_change_to_poster_photo_with_background_id? or
      saved_change_to_hero_frame_photo_id? or
      saved_change_to_family_frame_photo_id?)
      'user_poster_photos_changed'
    else
      nil
    end
  end

  def fetch_poster_affiliated_circle(circle_id: nil, category_id: nil, has_creative_id:,
                                     has_poster_layout_data:)
    poster_affiliated_party_id = self.get_poster_affiliated_party_id
    if has_poster_layout_data && poster_affiliated_party_id.present? && AppVersionSupport.posters_feed_enabled?
      affiliated_circle = Circle.find_by(id: poster_affiliated_party_id)
    elsif has_poster_layout_data && poster_affiliated_party_id.blank? && AppVersionSupport.posters_feed_enabled?
      affiliated_circle = Circle.find_by(id: Constants.public_circle_id)
    end

    if affiliated_circle.blank?
      affiliated_circle_id = self.affiliated_party_circle_id.to_i
      affiliated_circle = get_gradient_circle(affiliated_circle_id, circle_id, category_id, has_creative_id:)
    end

    affiliated_circle
  end

  def fetch_circle_frames(frame_circle: nil, category_kind:, has_active_layout:, has_circle_package:)
    # disabling circle frames temporarily
    return [] if true
    circle_frames = []
    if frame_circle.present? && category_kind.present? && has_active_layout && has_circle_package
      circle_frames = CircleFrame.get_circle_frames
    end
    circle_frames
  end

  def fetch_neutral_frames(is_from_edit_layouts, user_opted_frames, is_poster_subscribed, user_frame_ids_with_is_locked)
    neutral_frames = []
    # if it is from edit_layouts, then don't show neutral frame if it is not present in user opted frames
    unless is_from_edit_layouts
      neutral_frame = Frame.get_neutral_frame
      # check if neutral frame is present in user opted frames if not present, then add it to user_opted_frames
      # else skip the frame if user is not subscribed
      if neutral_frame.present? && !user_opted_frames.any? { |frame| frame.identifier == neutral_frame.identifier }
        neutral_frames << neutral_frame
        user_frame_ids_with_is_locked[neutral_frame.id] = !is_poster_subscribed
      end
    end
    neutral_frames
  end

  def generate_poster_return_url(category_id: nil, creative_id: nil, frame_id:)
    if category_id.present?
      "/posters/layout?id=#{category_id}&frame_id=#{frame_id}"
    elsif creative_id.present?
      "/posters/layout?creative_id=#{creative_id}&frame_id=#{frame_id}"
    end
  end

  def generate_poster_locked_deeplink(category_id: nil, creative_id: nil, frame_id:, user_photo_type: nil,
                                      is_dummy_photo: false, layout_locked:)
    return nil if !layout_locked && !is_dummy_photo

    return_url = generate_poster_return_url(category_id: category_id, creative_id: creative_id, frame_id: frame_id)
    base_url = is_eligible_for_start_trial? ? get_paywall_deeplink(source: "layout") : "/premium-experience?payment-sheet=true&source=layout"

    # If user_photo_type is provided, add it to the deeplink for poster-photo-selection
    if !layout_locked && is_dummy_photo && user_photo_type.present? && [Frame::USER_PHOTO_TYPES[:family_cutout], Frame::USER_PHOTO_TYPES[:hero_cutout]].include?(user_photo_type)
      photo_selection_url = "/poster-photo-selection?source=layout&user_photo_type=#{user_photo_type}"
      photo_selection_url += "&return_url=#{return_url}" if return_url.present?
      return photo_selection_url
    end

    return_url.present? ? "#{base_url}&return_url=#{return_url}" : base_url
  end

  def fetch_recommended_frames(is_from_edit_layouts, user_opted_frame_ids)
    recommended_frames = []
    if is_from_edit_layouts
      recommended_frames = UserRecommendedFrame.recommended_frames(user: self,
                                                                   exclude_frame_ids: user_opted_frame_ids)
    end
    recommended_frames
  end

  def fetch_poster_circle_ids_for_non_layout_user(circle_id, category_id, creative_id, user_role)
    user_joined_circle_ids = fetch_user_joined_circle_ids_for_non_layout_user_posters

    return [circle_id] if circle_id.present?

    if category_id.present?
      circle_ids = fetch_specific_circle_ids(user_role, category_id, user_joined_circle_ids, type: :category)
      return circle_ids if circle_ids.present?
    end

    circle_ids = fetch_specific_circle_ids(user_role, creative_id, user_joined_circle_ids, type: :creative)
    return circle_ids if circle_ids.present?

    # Default to public circle if no other IDs are found
    [0]
  end

  def fetch_specific_circle_ids(user_role, id, user_joined_circle_ids, type:)
    # If user has an affiliated party circle and is a poster user, return that circle plus state (and public)
    if user_role.present? && affiliated_party_circle_id.present? &&
       user_joined_circle_ids.include?(affiliated_party_circle_id)
      return [affiliated_party_circle_id, state_id]
    end

    # For poster users without affiliated party circle, if they have any user role, default to state.
    return [state_id] if user_role.present?

    # Otherwise, fetch the specific circles based on the type.
    specific_circle_ids = case type
                          when :category
                            EventCircle.where(event_id: id).pluck(:circle_id)
                          when :creative
                            PosterCreativeCircle.where(poster_creative_id: id).pluck(:circle_id)
                          else
                            []
                          end

    user_joined_circle_ids & specific_circle_ids
  end

  def fetch_user_joined_circle_ids_for_non_layout_user_posters
    joined_circles = affiliated_party_circle_id.present? ? [affiliated_party_circle_id] : get_user_joined_party_circle_ids
    if joined_circles.size > 1
      joined_circles.unshift(state_id, 0)
    else
      joined_circles + [state_id, 0]
    end
  end

  def get_user_layouts_for_non_layout_user(circle_id: nil, category_id: nil, creative_id: nil, user_role: nil,
                                           circle_frames: [], circle_common_hash_for_layout: {})
    user_layouts = []
    related_circle_ids_for_poster_creatives = fetch_poster_circle_ids_for_non_layout_user(circle_id, category_id, creative_id, user_role)
    related_circle_ids_for_poster_creatives.each do |circle_id|
      circle = Circle.find_by(id: circle_id)
      gradient_circle = circle
      if circle.political_leader_level?
        # add leader affiliation circle only so that party gradients will come up
        party_circle = circle.get_leader_circle_party
        gradient_circle = party_circle if party_circle.present?
      end
      if circle_frames.present?
        user_layouts += get_circle_layouts_for_non_layout_user(circle: circle, frames: circle_frames,
                                                               gradient_circle: gradient_circle,
                                                               common_hash: circle_common_hash_for_layout,
                                                               user_role: user_role)
      end
      basic_layouts = build_basic_frames_json(circle: gradient_circle, user_role: user_role)
      user_layouts += basic_layouts
    end
    user_layouts
  end

  def categorize_layouts(user_layouts, circle_frame_ids)
    layouts = Hash.new { |hash, key| hash[key] = [] }

    user_layouts.each do |layout|
      # send default Font configs for all layouts (this is for versions below posters_tab_v3_enabled?)
      layout[:fonts_config] ||= default_font_config(layout.dig(:identity, :is_user_position_back))
      category = layout_category(layout, circle_frame_ids)

      layouts[category] << layout
    end

    layouts
  end

  def layout_category(layout, circle_frame_ids)
    return :circle if layout[:id].in?(circle_frame_ids) && !layout[:is_basic_layout]
    return :basic if layout[:layout_type] == 'basic'
    return :premium_neutral if layout[:neutral_frame]

    :premium
  end

  def add_sponsorship_to_circle_layouts(circle_layouts, frame_circle)
    return if circle_layouts.blank? || frame_circle.blank?
    # sponsorship json of frame_circle
    sponsorship_json = frame_circle.sponsorship_json

    circle_layouts.each do |layout|
      layout[:sponsorship] = sponsorship_json
      # send show_praja_logo as false for circle_frames
      layout[:show_praja_logo] = false
    end
  end

  def order_premium_layouts!(premium_layouts)
    return premium_layouts if premium_layouts.empty?

    move_layout_of_the_day!(premium_layouts)
    reposition_specific_frame_type_layouts_in_premium_layouts!(premium_layouts)

    premium_layouts
  end

  # pick the layout of the day for user and place it first in the premium_layouts_list
  def move_layout_of_the_day!(premium_layouts)
    index_of_layout = (id + Time.zone.now.yday) % premium_layouts.length
    premium_layouts.unshift(premium_layouts.delete_at(index_of_layout))
  end

  def reposition_specific_frame_type_layouts_in_premium_layouts!(premium_layouts)
    family_layout = premium_layouts.find { |item| item[:frame_type] == 'family_frame_premium' }
    hero_layout = premium_layouts.find { |item| item[:frame_type] == 'hero_frame_premium' }

    premium_layouts.delete(family_layout) if family_layout
    premium_layouts.delete(hero_layout) if hero_layout
    # add family_frame_premium layout second in the premium_layouts_list
    premium_layouts.insert(1, family_layout) if family_layout
    # add hero_frame_premium layout third in the premium_layouts_list if family_frame_premium layout is present
    # or else add it second in the premium_layouts_list
    index_to_insert = family_layout ? 2 : 1
    premium_layouts.insert(index_to_insert, hero_layout) if hero_layout
  end

  def add_analytics_params_to_layouts(ordered_layouts, circle_id, user_role)
    ordered_layouts.each do |layout|
      next if skip_premium_pitch_analytics?(layout)

      layout[:analytics_params] = build_layout_analytics_params(layout, circle_id, user_role)
    end
  end

  def skip_premium_pitch_analytics?(layout)
    # skip if it is premium pitch bcz it analytics params are already present
    layout.dig(:analytics_params, :is_premium_pitch) == true
  end

  def build_layout_analytics_params(layout, circle_id, user_role)
    {
      layout_type: layout[:layout_type],
      is_locked: layout[:is_locked],
      golden_frame: layout[:golden_frame],
      header_1_count: layout[:header_1_photos]&.count,
      header_2_count: layout[:header_2_photos]&.count,
      identity_type: layout.dig(:identity, :type),
      is_user_position_back: layout.dig(:identity, :is_user_position_back),
      is_dummy_photo: layout.dig(:identity, :is_dummy_photo),
      party_icon_position: layout.dig(:party_icon, :position),
      circle_id: circle_id,
      frame_type: layout[:frame_type],
      is_circle_sponsored_frame: layout[:sponsorship].present?,
      is_circle_sponsored_frame_locked: layout[:fan_poster_request].present?,
      user_posters_subscription_status: get_subscription_status,
      user_badge: user_role.present? ? 'Yes' : 'No',
      user_badge_role: user_role&.get_role_name,
      has_layout: has_premium_layout?,
      has_layout_feedback_request: layout[:layout_feedback_request].present?,
    }.compact
  end

  def add_premium_or_fan_poster_request_layout(ordered_layouts, context)
    show_premium_pitch = can_show_pitch_premium?
    show_fan_poster_request_layout = can_show_fan_poster_request_layout?(context)
    premium_pitch_view_count = show_premium_pitch ? get_premium_pitch_view_count : 0
    fan_poster_request_view_count = show_fan_poster_request_layout ? get_fan_poster_view_count(context[:circle_id]) : 0
    fan_poster_request_layout = nil
    premium_pitch_inserted = false

    # Case 1: Add Premium Pitch at the start if eligible
    if show_premium_pitch && premium_pitch_view_count < 2
      add_premium_pitch!(ordered_layouts, context[:user_role], position: :start)
      premium_pitch_inserted = true
    end

    # Case 2: Add Fan Poster Request at the start if eligible
    if show_fan_poster_request_layout && fan_poster_request_view_count < 2
      fan_poster_request_layout = add_fan_poster_request!(ordered_layouts, context, position: :start)
    end

    # Case 3: If no fan poster request was added, add Premium Pitch at the end
    if show_premium_pitch && premium_pitch_view_count >= 2 && fan_poster_request_layout.blank?
      add_premium_pitch!(ordered_layouts, context[:user_role], position: :end)
      premium_pitch_inserted = true
    end

    # Case 4: Add Fan Poster Request at the end only if no Premium Pitch was inserted in Case 3
    if !premium_pitch_inserted && show_fan_poster_request_layout && fan_poster_request_view_count >= 2
      add_fan_poster_request!(ordered_layouts, context, position: :end)
    end
  end

  def can_show_pitch_premium?
    is_eligible_for_premium_pitch? || is_eligible_for_premium_pitch_v2?
  end

  # disabling temporarily
  def can_show_fan_poster_request_layout?(context)
    false && AppVersionSupport.can_show_fan_poster_request_layout? && category_kind != "congrats" &&
      context[:circle_frames].blank? && context[:circle_id].present? && context[:category_kind].present? &&
      context[:has_active_layout] && !context[:has_circle_package] && !owner_of_circle?(context[:circle_id]) &&
      !CirclePremiumInterest.has_fan_poster_interest?(circle_id: context[:circle_id], user_id: self.id)
  end

  def add_premium_pitch!(ordered_layouts, user_role, position:)
    pitch_premium_layout = get_pitch_premium_layout(user_role)
    return unless pitch_premium_layout.present?

    position == :start ? ordered_layouts.unshift(pitch_premium_layout) : ordered_layouts << pitch_premium_layout
  end

  def add_fan_poster_request!(ordered_layouts, context, position:)
    fan_poster_request_layout = get_fan_poster_layout(
      frame_circle: context[:frame_circle],
      user_poster_layout_id: context[:user_poster_layout_id],
      user_role: context[:user_role]
    )
    return unless fan_poster_request_layout.present?

    position == :start ? ordered_layouts.unshift(fan_poster_request_layout) : ordered_layouts << fan_poster_request_layout
    update_fan_poster_view_count(context[:circle_id])
    fan_poster_request_layout
  end

  def build_layouts_for_poster_layout_present_users(user_layouts, context)
    # get user opted frames with is_locked
    user_frame_ids_with_is_locked = get_user_opted_frame_ids_with_is_locked(is_poster_subscribed)
    user_opted_frames = Frame.get_frames_by_ids(user_frame_ids_with_is_locked.keys)
    user_opted_frame_ids = user_opted_frames.map(&:id)
    affiliated_circle = context[:affiliated_circle]
    party_icon_url = get_affiliated_party_icon(affiliated_circle.id) if affiliated_circle.present?
    context[:party_icon_url] = party_icon_url

    neutral_frames = fetch_neutral_frames(context[:is_from_edit_layouts], user_opted_frames,
                                          is_poster_subscribed, user_frame_ids_with_is_locked)
    recommended_frames = fetch_recommended_frames(context[:is_from_edit_layouts], user_opted_frame_ids)

    user_layout_variation, common_hash_for_layout = get_common_hash_for_layout_and_layout_variation(
      context[:user_poster_layout_id], entity: self)

    frames_to_process = user_opted_frames + recommended_frames + context[:circle_frames] + neutral_frames

    frames_to_process.each do |frame|
      user_layouts << build_layout_hash_for_poster_layout_present_users(frame, context, user_frame_ids_with_is_locked,
                                                                        user_layout_variation, common_hash_for_layout,
                                                                        user_opted_frame_ids, neutral_frames)
    end

    add_basic_frames_if_needed!(user_layouts, context)
  end

  def fetch_dummy_photo_info(frame)
    is_dummy_photo = false
    user_photo_type = nil

    case frame.frame_type.to_sym
    when :family_frame_premium
      user_photo_type = Frame::USER_PHOTO_TYPES[:family_cutout]
      is_dummy_photo = self.family_frame_photo.blank?
    when :hero_frame_premium
      user_photo_type = Frame::USER_PHOTO_TYPES[:hero_cutout]
      is_dummy_photo = self.hero_frame_photo.blank?
    end

    { is_dummy_photo: is_dummy_photo, user_photo_type: user_photo_type }
  end

  def build_layout_hash_for_poster_layout_present_users(frame, context, user_frame_ids_with_is_locked,
                                                        user_layout_variation, common_hash_for_layout,
                                                        user_opted_frame_ids, neutral_frames)
    frame_id = frame.id
    layout_type = frame_id.in?(context[:circle_frame_ids]) ? 'basic' : 'premium'
    layout_variation = frame_id.in?(context[:circle_frame_ids]) ? context[:circle_layout_variation] : user_layout_variation
    is_user_position_back = frame.user_position_back.nil? ? get_is_user_position_back_for_posters_tab_v2 : frame.user_position_back
    show_badge_ribbon = frame.override_badge_strip_value(context[:user_role].present?, !context[:custom_role_name].blank?)

    circle = frame.is_neutral_frame ? Circle.find_by(id: 0) : context[:affiliated_circle]
    layout = get_layout(layout_type, frame.gold_border, frame.has_shadow_color, circle,
                        context[:user_role], layout_variation, context[:party_icon_url], frame.identity_type,
                        context[:user_poster_layout_id], original_layout_type: frame.frame_type)

    add_identity_details!(layout, frame, context, is_user_position_back, circle)
    merge_common_hash!(layout, frame_id, context, common_hash_for_layout)

    layout[:is_locked] = user_frame_ids_with_is_locked[frame_id] || false
    dummy_photo_info = fetch_dummy_photo_info(frame)
    is_dummy_photo = dummy_photo_info[:is_dummy_photo]
    user_photo_type = dummy_photo_info[:user_photo_type]
    # Pass user_photo_type for family and hero frame cutouts
    layout[:locked_deeplink] = generate_poster_locked_deeplink(category_id: context[:category_id],
                                                               creative_id: context[:creative_id],
                                                               frame_id: frame_id,
                                                               user_photo_type: user_photo_type,
                                                               is_dummy_photo: is_dummy_photo,
                                                               layout_locked: layout[:is_locked])
    layout[:layout_deeplink] = generate_poster_return_url(category_id: context[:category_id],
                                                          creative_id: context[:creative_id], frame_id: frame_id)
    layout[:id] = frame_id
    layout[:frame_id] = frame_id
    layout[:protocol_id] = determine_protocol_id(frame_id, context)
    layout[:is_selected] = user_opted_frame_ids.include?(frame.id)
    layout[:fonts_config] = frame.font&.frame_json_data
    layout[:is_bordered_layout] = frame.identity_type == 'glassy_user'
    layout[:neutral_frame] = frame.is_neutral_frame
    layout[:enable_outer_frame] = frame.outer_frame
    layout[:frame_type] = frame.frame_type

    clear_header_photos_if_needed!(layout, frame)
    determine_badge_text_color!(layout, frame, show_badge_ribbon)
    adjust_party_icon!(layout, frame, is_user_position_back)
    add_feedback_request!(layout, context, frame_id, user_opted_frame_ids, neutral_frames)

    layout
  end

  # don't send header_1_photos and header_2_photos if it is_neutral_frame or family_frame layout
  def clear_header_photos_if_needed!(layout, frame)
    return unless should_clear_header_photos?(frame)

    layout[:header_1_photos] = []
    layout[:header_2_photos] = []
  end

  def should_clear_header_photos?(frame)
    frame.is_neutral_frame || frame.frame_type == 'family_frame_premium'
  end

  # send text color instead of badge text color if show badge banner is false
  def determine_badge_text_color!(layout, frame, show_badge_ribbon)
    return if show_badge_ribbon

    layout[:badge_text_color] = layout[:text_color] if layout[:text_color].present?
    layout[:badge_text_color] = 0xff000000 if frame.identity_type == 'gold_lettered_user'
  end

  def add_identity_details!(layout, frame, context, is_user_position_back, circle)
    return unless layout[:identity]

    # Get dummy photo info for the frame
    dummy_photo_info = fetch_dummy_photo_info(frame)
    is_dummy_photo = dummy_photo_info[:is_dummy_photo]

    layout[:identity].merge!(
      type: frame.identity_type,
      user_photo_type: frame.user_photo_type,
      is_user_position_back: is_user_position_back,
      show_badge_ribbon: frame.override_badge_strip_value(context[:user_role].present?, !context[:custom_role_name].blank?),
      party_highlight_color_primary: circle.get_primary_highlight_color(frame.identity_type),
      party_highlight_color_secondary: circle.get_secondary_highlight_color(frame.identity_type),
      slogan_text: get_slogan(frame.identity_type, context[:user_role], circle),
      slogan_icon_url: get_slogan_icon_url(frame.identity_type, circle),
      party_icon_url: layout[:party_icon] ? context[:party_icon_url] : nil,
      is_dummy_photo: is_dummy_photo
    )
    layout[:identity].compact!
  end

  def merge_common_hash!(layout, frame_id, context, common_hash_for_layout)
    layout.merge!(frame_id.in?(context[:circle_frame_ids]) ? context[:circle_common_hash_for_layout] : common_hash_for_layout)
  end

  def determine_protocol_id(frame_id, context)
    # we are using same function in both Posters Web Dashboard as well as for App
    # So people are trying to open the posters preview for inactive layouts
    # So, we are using & while accessing protocol_id, to avoid null errors
    frame_id.in?(context[:circle_frame_ids]) ? context[:circle_poster_layout_protocol]&.id.to_s : context[:user_poster_layout_protocol]&.id.to_s
  end

  def adjust_party_icon!(layout, frame, is_user_position_back)
    # return unless layout[:identity]

    if frame.is_neutral_frame || frame.identity_type == 'flat_user_badge_circle'
      layout[:party_icon] = nil
      layout[:identity][:party_icon_url] = nil
    elsif frame.has_footer_party_icon && Constants.posters_v3_released_identities.include?(frame.identity_type)
      layout[:party_icon] = nil
    elsif !is_user_position_back && frame.has_footer_party_icon
      layout[:party_icon] = nil
    else
      layout[:identity][:party_icon_url] = nil
    end
  end

  def add_feedback_request!(layout, context, frame_id, user_opted_frame_ids, neutral_frames)
    if (AppVersionSupport.layout_feedback_prompt_v2_enabled? && self.layout_approval_enabled?) &&
       context[:user_poster_layout_protocol]&.entity.is_a?(User) &&
       context[:user_poster_layout_protocol]&.awaited_review_status? &&
       frame_id.in?(user_opted_frame_ids + neutral_frames.map(&:id))
      layout[:layout_feedback_request] = AppVersionSupport.layout_feedback_prompt_v2_enabled? ? get_layout_feedback_prompt_v2 : get_layout_feedback_prompt
    end
  end

  def add_basic_frames_if_needed!(user_layouts, context)
    # add basic frame if user not subscribed to posters yet
    return if is_poster_subscribed || context[:is_from_edit_layouts]

    user_layouts.concat(build_basic_frames_json(circle: context[:affiliated_circle], user_role: context[:user_role]))
  end

  def order_layouts(context)
    return [] if all_layouts_empty?(context)

    return order_for_poster_subscribed_users(context) if context[:is_poster_subscribed] && context[:has_poster_layout_data]
    return order_for_non_subscribed_users_when_creative_present(context) if context[:creative_id].present? && !context[:is_poster_subscribed]
    return order_for_non_subscribed_users_when_creative_absent(context) unless context[:is_poster_subscribed]

    default_layouts_order(context)
  end

  def all_layouts_empty?(context)
    context[:premium_layouts].empty? && context[:circle_layouts].empty? &&
      context[:premium_neutral_layouts].empty? && context[:basic_layouts].empty?
  end

  def order_for_poster_subscribed_users(context)
    context[:premium_layouts] + context[:circle_layouts] + context[:premium_neutral_layouts]
  end

  def order_for_non_subscribed_users_when_creative_present(context)
    premium_layouts = context[:premium_layouts]
    circle_layouts = context[:circle_layouts]
    premium_neutral_layouts = context[:premium_neutral_layouts]
    basic_layouts = context[:basic_layouts]

    # if creative_id is present and premium_layouts are blank, then we are considering that user is coming from channel
    # If source is channel, first circle frames and then basic frames because user don't have premium frames
    return circle_layouts.present? ? circle_layouts : basic_layouts if premium_layouts.blank?

    # insert basic layout at the end if user is not subscribed and layout is created in last 2 days.
    if context[:is_eligible_for_basic_frames_at_last].call
      return circle_layouts.present? ? circle_layouts + premium_layouts + premium_neutral_layouts : premium_layouts + premium_neutral_layouts + basic_layouts
    end

    # if creative_id is present and premium_layouts are present, then we are considering that user is coming from channel
    circle_layouts.present? ? circle_layouts + premium_layouts + premium_neutral_layouts : insert_basic_layouts(premium_layouts, basic_layouts, premium_neutral_layouts)
  end

  def order_for_non_subscribed_users_when_creative_absent(context)
    return order_basic_frames(context) if context[:circle_frames].blank? && context[:creative_id].blank?
    return order_with_circle_frames(context) if context[:circle_frames].present? && context[:creative_id].blank?

    default_layouts_order(context)
  end

  def order_basic_frames(context)
    premium_layouts = context[:premium_layouts]
    basic_layouts = context[:basic_layouts]
    premium_neutral_layouts = context[:premium_neutral_layouts]
    # insert basic layout after the first premium layout if premium layouts are present else add neutral basic layout to the end
    premium_layouts.present? ? insert_basic_layouts(premium_layouts, basic_layouts, premium_neutral_layouts) : basic_layouts
  end

  def order_with_circle_frames(context)
    premium_layouts = context[:premium_layouts]
    circle_layouts = context[:circle_layouts]
    premium_neutral_layouts = context[:premium_neutral_layouts]

    # insert basic layout at the end if user is not subscribed and layout is created in last 2 days.
    if context[:is_eligible_for_basic_frames_at_last].call
      return premium_layouts + premium_neutral_layouts + circle_layouts
    end

    insert_circle_layouts(premium_layouts, circle_layouts, premium_neutral_layouts)
  end

  def insert_basic_layouts(premium_layouts, basic_layouts, premium_neutral_layouts)
    premium_layouts.insert(1, *basic_layouts).compact + premium_neutral_layouts
  end

  # insert all the circle layouts after the first premium layout
  def insert_circle_layouts(premium_layouts, circle_layouts, premium_neutral_layouts)
    premium_layouts.insert(1, *circle_layouts).compact + premium_neutral_layouts
  end

  def default_layouts_order(context)
    context[:premium_layouts] + context[:circle_layouts] + context[:premium_neutral_layouts] + context[:basic_layouts]
  end
end
