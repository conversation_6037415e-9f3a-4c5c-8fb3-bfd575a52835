<!DOCTYPE html>
  <html lang="te">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>User Badge Template</title>
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Telugu:wght@400;500;700&family=Anek+Telugu:wght@400;500;700&display=swap');

      :root {
        --container-width: 720px;
        --container-height: 100px;
        --user-photo-width: 116px;
        --text-padding: 10px;
        --left-padding: 132px;
        --name-font-size: 30px;
        --badge-font-size: 20px;
        --name-font-family: 'Noto Serif Telugu';
        --badge-font-family: 'Noto Serif Telugu';
      }

      #body {
        background: black;
      }

      #top-outer-container {
        width: var(--container-width);
        height: var(--container-height);
        position: relative;
        margin: 20px auto;
        background: white;
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: center;
        padding: var(--text-padding);
        box-sizing: border-box;
      }

      #user-photo-placeholder {
        width: var(--user-photo-width);
        height: var(--container-height);
      }

      #outer-container {
        width: var(--container-width);
        height: var(--container-height);
        position: relative;
        background: white;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        padding: var(--text-padding);
        box-sizing: border-box;
      }

      #user-name {
        font-family: var(--name-font-family), 'Noto Sans Telugu', sans-serif;
        font-size: var(--name-font-size);
        font-weight: 700;
        color: #333;
        margin: 0;
        line-height: 1.2;
        word-wrap: break-word;
        text-align: center;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
      }

      #badge-description {
        font-family: var(--badge-font-family), 'Noto Sans Telugu', sans-serif;
        font-size: var(--badge-font-size);
        font-weight: 500;
        color: #666;
        margin: 0;
        line-height: 1.3;
        word-wrap: break-word;
        text-align: center;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
      }

      body {
        background: white;
        font-family: 'Noto Sans Telugu', sans-serif;
        margin: 0;
        padding: 0;
      }

      * {
        box-sizing: border-box;
      }
    </style>
  </head>

  <body id="body">
    <div id="top-outer-container">
      <div id="user-photo-placeholder"></div>
      <div id="outer-container">
        <h1 id="user-name">Fantom User 1</h1>
        <p id="badge-description">YCP Top Fan</p>
      </div>
    </div>
  </body>
  </html>
