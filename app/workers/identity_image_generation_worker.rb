# frozen_string_literal: true

class IdentityImageGenerationWorker
  include Sidekiq::Worker
  include Sidekiq::Throttled::Worker
  include Capture
  
  sidekiq_options queue: :video_posters_generation, retry: 3, lock: :until_and_while_executing, on_conflict: :log
  
  sidekiq_throttle(
    concurrency: {
      limit: 10,
    },
    threshold: {
      limit: 30,
      period: 5.seconds,
    }
  )

  sidekiq_retries_exhausted do |msg, ex|
    Honeybadger.notify(ex, context: { args: msg["args"] })
    Rails.logger.error("IdentityImageGenerationWorker retries exhausted: #{ex.message}")
  end

  def perform(user_id, video_frame_id)
    Honeybadger.context({ user_id: user_id, video_frame_id: video_frame_id })
    
    user = User.find_by_id(user_id)
    return if user.blank?

    video_frame = VideoFrame.find_by_id(video_frame_id)
    return if video_frame.blank?

    user_video_frame = UserVideoFrame.find_by(user: user, video_frame: video_frame)
    return if user_video_frame.blank?

    # Generate identity image based on video frame type
    video_type = video_frame.video_type
    template_name = get_template_name(video_type)
    
    # Get user data for identity image
    user_name = user.name
    badge_description = user.get_badge_role&.get_description
    
    # Get font information from video frame
    font = video_frame.font
    name_font_family = font.name_font
    badge_font_family = font.badge_font
    
    # Generate HTML content
    html = generate_html(
      template_name: template_name,
      video_type: video_type,
      user_name: user_name,
      badge_description: badge_description,
      name_font_family: name_font_family,
      badge_font_family: badge_font_family
    )
    
    # Capture HTML as image
    uploaded_image = capture_html_as_image(html, '#top-outer-container')
    raise "Did not receive url from captured html - #{uploaded_image}" unless uploaded_image['cdn_url'].present?

    # Update user_video_frame with the generated identity image URL
    user_video_frame.update!(identity_photo_url: uploaded_image['cdn_url'])
    
    Rails.logger.info("Identity image generated successfully for user #{user_id}, video_frame #{video_frame_id}")
    
  rescue StandardError => e
    Honeybadger.notify(e, context: { user_id: user_id, video_frame_id: video_frame_id })
    Rails.logger.error("IdentityImageGenerationWorker failed: #{e.message}")
    raise
  end

  private

  def get_template_name(video_type)
    # Use unified template for all video types
    'identity_unified'
  end

  def generate_html(template_name:, video_type:, user_name:, badge_description:, name_font_family:, badge_font_family:)
    # Get dimensions and styling based on video type
    dimensions = UserVideoPoster::IDENTITY_IMAGE_DIMENSIONS[video_type]
    user_photo_dimensions = UserVideoPoster::USER_PHOTO_DIMENSIONS[video_type]

    # Calculate font sizes based on name length with improved logic
    name_font_size = calculate_name_font_size(user_name)

    # Determine user photo width based on video type (0px for portrait, actual width for landscape)
    user_photo_width = video_type == 'portrait' ? 0 : user_photo_dimensions[:width] + 50

    locals = {
      container_width: dimensions[:width]*2,
      container_height: dimensions[:height]*2,
      text_padding: UserVideoPoster::IDENTITY_TEXT_PADDING,
      name_font_size: name_font_size,
      badge_font_size: UserVideoPoster::IDENTITY_BADGE_FONT_SIZE,
      name_font_family: name_font_family,
      badge_font_family: badge_font_family,
      user_name: user_name,
      badge_description: badge_description,
      user_photo_width: user_photo_width,
      left_padding: UserVideoPoster::IDENTITY_LEFT_PADDING_LANDSCAPE
    }

    ActionController::Base.render(
      inline: File.read(Rails.root.join('app', 'views', 'identity_images', "#{template_name}.html.erb")),
      locals: locals
    )
  end

  def calculate_name_font_size(user_name)
    name_length = user_name.length

    case name_length
    when 0..UserVideoPoster::IDENTITY_NAME_LENGTH_MEDIUM_THRESHOLD
      UserVideoPoster::IDENTITY_NAME_FONT_SIZE_LARGE
    when (UserVideoPoster::IDENTITY_NAME_LENGTH_MEDIUM_THRESHOLD + 1)..UserVideoPoster::IDENTITY_NAME_LENGTH_SMALL_THRESHOLD
      UserVideoPoster::IDENTITY_NAME_FONT_SIZE_MEDIUM
    when (UserVideoPoster::IDENTITY_NAME_LENGTH_SMALL_THRESHOLD + 1)..UserVideoPoster::IDENTITY_NAME_LENGTH_MIN_THRESHOLD
      UserVideoPoster::IDENTITY_NAME_FONT_SIZE_SMALL
    else
      UserVideoPoster::IDENTITY_NAME_FONT_SIZE_MIN
    end
  end
end
