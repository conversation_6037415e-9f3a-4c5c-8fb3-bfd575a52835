require 'rails_helper'

RSpec.describe IdentityImageGenerationWorker, type: :worker do
  let(:user) { FactoryBot.create(:user) }
  let(:font) do
    Font.find_by(name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu") ||
      FactoryBot.create(:font, name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu")
  end
  let(:video_frame) { FactoryBot.create(:video_frame, font: font) }
  let(:user_video_frame) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame) }
  let(:worker) { described_class.new }

  describe '#perform' do
    context 'with valid user and video frame' do
      before do
        allow(worker).to receive(:generate_html).and_return('<div id="outer-container">Test HTML</div>')
        allow(worker).to receive(:capture_html_as_image).and_return({
          'cdn_url' => 'https://example.com/identity-image.png',
          'service' => 'aws',
          'file_name' => 'identity-image.png'
        })
      end

      it 'generates identity image successfully' do
        expect(user_video_frame.identity_photo_url).to eq("https://example.com/identity_photo.jpg")
        
        worker.perform(user.id, video_frame.id)
        
        user_video_frame.reload
        expect(user_video_frame.identity_photo_url).to eq('https://example.com/identity-image.png')
      end

      it 'calls capture_html_as_image with correct parameters' do
        expect(worker).to receive(:capture_html_as_image).with(
          '<div id="outer-container">Test HTML</div>',
          '#outer-container'
        ).and_return({
          'cdn_url' => 'https://example.com/identity-image.png'
        })

        worker.perform(user.id, video_frame.id)
      end

      it 'generates HTML with correct template for portrait video type' do
        portrait_frame = FactoryBot.create(:video_frame, video_type: 'portrait', font: font)
        portrait_user_frame = FactoryBot.create(:user_video_frame, user: user, video_frame: portrait_frame)

        expect(worker).to receive(:generate_html).with(
          hash_including(
            template_name: 'identity_portrait',
            video_type: 'portrait',
            user_name: user.name
          )
        ).and_return('<div>HTML</div>')

        worker.perform(user.id, portrait_frame.id)
      end

      it 'generates HTML with correct template for landscape video type' do
        landscape_frame = FactoryBot.create(:video_frame, video_type: 'landscape', font: font)
        landscape_user_frame = FactoryBot.create(:user_video_frame, user: user, video_frame: landscape_frame)

        expect(worker).to receive(:generate_html).with(
          hash_including(
            template_name: 'identity_landscape',
            video_type: 'landscape',
            user_name: user.name
          )
        ).and_return('<div>HTML</div>')

        worker.perform(user.id, landscape_frame.id)
      end
    end

    context 'with invalid parameters' do
      it 'returns early when user is not found' do
        expect(worker).not_to receive(:capture_html_as_image)
        worker.perform(999999, video_frame.id)
      end

      it 'returns early when video frame is not found' do
        expect(worker).not_to receive(:capture_html_as_image)
        worker.perform(user.id, 999999)
      end

      it 'returns early when user_video_frame is not found' do
        other_user = FactoryBot.create(:user)
        expect(worker).not_to receive(:capture_html_as_image)
        worker.perform(other_user.id, video_frame.id)
      end
    end

    context 'when capture fails' do
      before do
        allow(worker).to receive(:generate_html).and_return('<div>HTML</div>')
      end

      it 'raises error when capture returns no URL' do
        allow(worker).to receive(:capture_html_as_image).and_return({})
        
        expect {
          worker.perform(user.id, video_frame.id)
        }.to raise_error(/Did not receive url from captured html/)
      end

      it 'raises error when capture returns blank URL' do
        allow(worker).to receive(:capture_html_as_image).and_return({ 'cdn_url' => '' })
        
        expect {
          worker.perform(user.id, video_frame.id)
        }.to raise_error(/Did not receive url from captured html/)
      end
    end

    context 'error handling' do
      it 'notifies Honeybadger on error' do
        allow(worker).to receive(:generate_html).and_raise(StandardError.new('Test error'))
        
        expect(Honeybadger).to receive(:notify).with(
          instance_of(StandardError),
          context: { user_id: user.id, video_frame_id: video_frame.id }
        )

        expect {
          worker.perform(user.id, video_frame.id)
        }.to raise_error(StandardError, 'Test error')
      end
    end
  end

  describe '#get_template_name' do
    it 'returns identity_unified for portrait video type' do
      expect(worker.send(:get_template_name, 'portrait')).to eq('identity_unified')
    end

    it 'returns identity_unified for landscape video type' do
      expect(worker.send(:get_template_name, 'landscape')).to eq('identity_unified')
    end

    it 'returns identity_unified for square video type' do
      expect(worker.send(:get_template_name, 'square')).to eq('identity_unified')
    end

    it 'returns identity_unified for unknown video type' do
      expect(worker.send(:get_template_name, 'unknown')).to eq('identity_unified')
    end
  end

  describe '#calculate_name_font_size' do
    it 'returns large font size for short names' do
      expect(worker.send(:calculate_name_font_size, 'John')).to eq(UserVideoPoster::IDENTITY_NAME_FONT_SIZE_LARGE)
    end

    it 'returns medium font size for medium length names' do
      expect(worker.send(:calculate_name_font_size, 'John Smith')).to eq(UserVideoPoster::IDENTITY_NAME_FONT_SIZE_MEDIUM)
    end

    it 'returns small font size for long names' do
      expect(worker.send(:calculate_name_font_size, 'Very Long Name')).to eq(UserVideoPoster::IDENTITY_NAME_FONT_SIZE_SMALL)
    end

    it 'returns minimum font size for very long names' do
      expect(worker.send(:calculate_name_font_size, 'Extremely Very Long Name Here')).to eq(UserVideoPoster::IDENTITY_NAME_FONT_SIZE_MIN)
    end
  end
end
