require 'rails_helper'

RSpec.describe "Identity Image Regeneration Integration", type: :integration do
  let(:user) { FactoryBot.create(:user, name: "Test User") }
  let(:parent_circle) { FactoryBot.create(:circle, circle_type: :interest, level: :political_party) }
  let(:purview_circle) { FactoryBot.create(:circle, circle_type: :location, level: :state) }
  let(:role) { FactoryBot.create(:role, has_purview: true, purview_level: :state) }
  let(:user_role) { FactoryBot.create(:user_role, user: user, role: role, parent_circle_id: parent_circle.id, purview_circle_id: purview_circle.id) }

  before do
    # Clear any existing jobs
    Sidekiq::Worker.clear_all
  end

  describe "User name change triggers identity image regeneration" do
    it "should enqueue RegenerateUserIdentityImagesWorker when user name changes" do
      expect {
        user.update!(name: "Updated User Name")
      }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(1)

      job = RegenerateUserIdentityImagesWorker.jobs.last
      expect(job['args']).to eq([user.id])
    end

    it "should not enqueue worker when other user fields change" do
      expect {
        user.update!(phone: 9876543210)
      }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
    end
  end

  describe "UserRole badge description changes trigger identity image regeneration" do
    it "should enqueue worker when role_id changes" do
      new_role = FactoryBot.create(:role)
      
      expect {
        user_role.update!(role: new_role)
      }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(1)

      job = RegenerateUserIdentityImagesWorker.jobs.last
      expect(job['args']).to eq([user.id])
    end

    it "should enqueue worker when parent_circle_id changes" do
      new_parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      
      expect {
        user_role.update!(parent_circle_id: new_parent_circle.id)
      }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(1)

      job = RegenerateUserIdentityImagesWorker.jobs.last
      expect(job['args']).to eq([user.id])
    end

    it "should enqueue worker when free_text changes" do
      expect {
        user_role.update!(free_text: "New badge text")
      }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(1)

      job = RegenerateUserIdentityImagesWorker.jobs.last
      expect(job['args']).to eq([user.id])
    end

    it "should not enqueue worker when non-badge description fields change" do
      expect {
        user_role.update!(badge_color: 'SILVER')
      }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
    end
  end

  describe "Role changes trigger identity image regeneration for all users" do
    let(:user2) { FactoryBot.create(:user, name: "Test User 2") }
    let!(:user_role2) { FactoryBot.create(:user_role, user: user2, role: role, parent_circle_id: parent_circle.id, purview_circle_id: purview_circle.id) }

    it "should enqueue workers for all users when role name changes" do
      expect {
        role.update!(name: "Updated Role Name")
      }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(2)

      job_args = RegenerateUserIdentityImagesWorker.jobs.map { |job| job['args'].first }
      expect(job_args).to contain_exactly(user.id, user2.id)
    end

    it "should enqueue workers for all users when display_name_order changes" do
      expect {
        role.update!(display_name_order: "role,parent,purview")
      }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(2)

      job_args = RegenerateUserIdentityImagesWorker.jobs.map { |job| job['args'].first }
      expect(job_args).to contain_exactly(user.id, user2.id)
    end

    it "should not enqueue workers when non-badge description fields change" do
      expect {
        role.update!(badge_color: 'SILVER')
      }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
    end
  end

  describe "UserPosterLayout creation triggers identity image regeneration" do
    it "should enqueue worker when new UserPosterLayout is created for a user" do
      expect {
        FactoryBot.create(:user_poster_layout, entity: user, h1_count: 1, h2_count: 1)
      }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(1)

      job = RegenerateUserIdentityImagesWorker.jobs.last
      expect(job['args']).to eq([user.id])
    end

    it "should not enqueue worker when UserPosterLayout is created for non-user entity" do
      circle = FactoryBot.create(:circle)

      expect {
        FactoryBot.create(:user_poster_layout, entity: circle, h1_count: 1, h2_count: 1)
      }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
    end
  end

  describe "End-to-end identity image regeneration flow with improved lifecycle" do
    let(:font) { FactoryBot.create(:font, name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu") }
    let(:video_frame1) { FactoryBot.create(:video_frame, video_type: 'portrait', font: font) }
    let(:video_frame2) { FactoryBot.create(:video_frame, video_type: 'landscape', font: font) }
    let!(:user_video_frame1) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame1, identity_photo_url: 'https://example.com/old_portrait.png') }
    let!(:user_video_frame2) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame2, identity_photo_url: 'https://example.com/old_landscape.png') }

    before do
      # Mock the capture_html_as_image method to avoid external dependencies
      allow_any_instance_of(RegenerateUserIdentityImagesWorker).to receive(:capture_html_as_image).and_return({
        'cdn_url' => 'https://example.com/new_identity_image.png'
      })
    end

    it "should maintain active frames during regeneration and only deactivate after success" do
      # Verify initial state - both frames are active
      expect(user_video_frame1.reload.active).to be true
      expect(user_video_frame2.reload.active).to be true
      expect(UserVideoFrame.where(user: user, active: true).count).to eq(2)

      # Trigger the change
      user.update!(name: "New User Name")

      # Process the job
      RegenerateUserIdentityImagesWorker.drain

      # Verify old frames are now inactive
      expect(user_video_frame1.reload.active).to be false
      expect(user_video_frame2.reload.active).to be false

      # Verify new frames are created and active
      new_frames = UserVideoFrame.where(user: user, active: true)
      expect(new_frames.count).to eq(2)

      new_frames.each do |frame|
        expect(frame.identity_photo_url).to eq('https://example.com/new_identity_image.png')
        expect(frame.id).not_to be_in([user_video_frame1.id, user_video_frame2.id])
      end
    end

    it "should keep old frames active if new image generation fails" do
      # Mock failure for image generation
      allow_any_instance_of(RegenerateUserIdentityImagesWorker).to receive(:capture_html_as_image).and_raise("Image generation failed")

      # Verify initial state
      expect(user_video_frame1.reload.active).to be true
      expect(user_video_frame2.reload.active).to be true

      # Trigger the change and expect it to raise an error
      user.update!(name: "New User Name")

      expect {
        RegenerateUserIdentityImagesWorker.drain
      }.to raise_error("Image generation failed")

      # Verify old frames remain active since generation failed
      expect(user_video_frame1.reload.active).to be true
      expect(user_video_frame2.reload.active).to be true

      # Verify no new frames were created
      expect(UserVideoFrame.where(user: user, active: true).count).to eq(2)
    end

    it "should handle partial failures gracefully" do
      call_count = 0
      allow_any_instance_of(RegenerateUserIdentityImagesWorker).to receive(:capture_html_as_image) do
        call_count += 1
        if call_count == 1
          { 'cdn_url' => 'https://example.com/new_portrait.png' }
        else
          raise "Second image generation failed"
        end
      end

      # Trigger the change
      user.update!(name: "New User Name")

      # Process the job - should not raise error due to graceful handling
      expect {
        RegenerateUserIdentityImagesWorker.drain
      }.not_to raise_error

      # Should have at least one successful generation
      # Old frames should remain active for failed generations
      active_frames = UserVideoFrame.where(user: user, active: true)
      expect(active_frames.count).to be >= 1
    end
  end
end
