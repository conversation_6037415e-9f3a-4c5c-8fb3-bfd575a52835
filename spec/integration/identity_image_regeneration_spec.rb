require 'rails_helper'

RSpec.describe "Identity Image Regeneration Integration", type: :integration do
  let(:user) { FactoryBot.create(:user, name: "Test User") }
  let(:parent_circle) { FactoryBot.create(:circle, circle_type: :interest, level: :political_party) }
  let(:purview_circle) { FactoryBot.create(:circle, circle_type: :location, level: :state) }
  let(:role) { FactoryBot.create(:role, has_purview: true, purview_level: :state) }
  let(:user_role) { FactoryBot.create(:user_role, user: user, role: role, parent_circle_id: parent_circle.id, purview_circle_id: purview_circle.id) }

  before do
    # Clear any existing jobs
    Sidekiq::Worker.clear_all
  end

  describe "User name change triggers identity image regeneration" do
    it "should enqueue RegenerateUserIdentityImagesWorker when user name changes" do
      expect {
        user.update!(name: "Updated User Name")
      }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(1)

      job = RegenerateUserIdentityImagesWorker.jobs.last
      expect(job['args']).to eq([user.id])
    end

    it "should not enqueue worker when other user fields change" do
      expect {
        user.update!(phone: 9876543210)
      }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
    end
  end

  describe "UserRole badge description changes trigger identity image regeneration" do
    it "should enqueue worker when role_id changes" do
      new_role = FactoryBot.create(:role)
      
      expect {
        user_role.update!(role: new_role)
      }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(1)

      job = RegenerateUserIdentityImagesWorker.jobs.last
      expect(job['args']).to eq([user.id])
    end

    it "should enqueue worker when parent_circle_id changes" do
      new_parent_circle = FactoryBot.create(:circle, circle_type: :interest, level: :political_party)
      
      expect {
        user_role.update!(parent_circle_id: new_parent_circle.id)
      }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(1)

      job = RegenerateUserIdentityImagesWorker.jobs.last
      expect(job['args']).to eq([user.id])
    end

    it "should enqueue worker when free_text changes" do
      expect {
        user_role.update!(free_text: "New badge text")
      }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(1)

      job = RegenerateUserIdentityImagesWorker.jobs.last
      expect(job['args']).to eq([user.id])
    end

    it "should not enqueue worker when non-badge description fields change" do
      expect {
        user_role.update!(badge_color: 'SILVER')
      }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
    end
  end

  describe "Role changes trigger identity image regeneration for all users" do
    let(:user2) { FactoryBot.create(:user, name: "Test User 2") }
    let!(:user_role2) { FactoryBot.create(:user_role, user: user2, role: role, parent_circle_id: parent_circle.id, purview_circle_id: purview_circle.id) }

    it "should enqueue workers for all users when role name changes" do
      expect {
        role.update!(name: "Updated Role Name")
      }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(2)

      job_args = RegenerateUserIdentityImagesWorker.jobs.map { |job| job['args'].first }
      expect(job_args).to contain_exactly(user.id, user2.id)
    end

    it "should enqueue workers for all users when display_name_order changes" do
      expect {
        role.update!(display_name_order: "role,parent,purview")
      }.to change(RegenerateUserIdentityImagesWorker.jobs, :size).by(2)

      job_args = RegenerateUserIdentityImagesWorker.jobs.map { |job| job['args'].first }
      expect(job_args).to contain_exactly(user.id, user2.id)
    end

    it "should not enqueue workers when non-badge description fields change" do
      expect {
        role.update!(badge_color: 'SILVER')
      }.not_to change(RegenerateUserIdentityImagesWorker.jobs, :size)
    end
  end

  describe "End-to-end identity image regeneration flow" do
    let(:font) { FactoryBot.create(:font, name_font: "Noto Sans Telugu", badge_font: "Noto Sans Telugu") }
    let(:video_frame) { FactoryBot.create(:video_frame, font: font) }
    let!(:user_video_frame) { FactoryBot.create(:user_video_frame, user: user, video_frame: video_frame) }

    before do
      # Mock the capture_html_as_image method to avoid external dependencies
      allow_any_instance_of(RegenerateUserIdentityImagesWorker).to receive(:capture_html_as_image).and_return({
        'cdn_url' => 'https://example.com/new_identity_image.png'
      })
    end

    it "should regenerate identity images when user name changes" do
      # Trigger the change
      user.update!(name: "New User Name")
      
      # Process the job
      RegenerateUserIdentityImagesWorker.drain
      
      # Verify the old user_video_frame is marked inactive
      expect(user_video_frame.reload.active).to be false
      
      # Verify a new user_video_frame is created
      new_user_video_frame = UserVideoFrame.where(user: user, active: true).first
      expect(new_user_video_frame).to be_present
      expect(new_user_video_frame.identity_photo_url).to eq('https://example.com/new_identity_image.png')
    end
  end
end
